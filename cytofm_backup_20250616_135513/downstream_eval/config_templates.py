#!/usr/bin/env python3
"""
配置模板文件 - 提供常用的参数配置组合
Configuration Templates - Provides common parameter combinations
"""

# ========================================
# 预定义配置模板
# ========================================

# 快速测试配置 - 用于快速验证代码功能
QUICK_TEST_CONFIG = {
    'models': ['ResNet50'],
    'datasets': ['Herlev'],
    'batch_size': 32,
    'epochs': 5,
    'lr': 1e-3,
    'gpu': 0,
    'compute_ci': False,
    'disable_progress_bar': True
}

# 标准评估配置 - 用于正常的模型评估
STANDARD_EVAL_CONFIG = {
    'models': ['CLIP', 'SigLIP', 'SigLIP2-ViT-L'],
    'datasets': ['Herlev', 'HiCervix', 'FNAC2019'],
    'batch_size': 64,
    'epochs': 50,
    'lr': 1e-3,
    'gpu': 0,
    'compute_ci': True,
    'n_bootstraps': 1000,
    'metrics': ['accuracy', 'auc', 'macro_f1']
}

# 高性能配置 - 用于有充足计算资源的情况
HIGH_PERFORMANCE_CONFIG = {
    'models': ['CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M'],
    'datasets': ['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed'],
    'batch_size': 128,
    'epochs': 100,
    'lr': 5e-4,
    'gpu': 0,
    'cache_data': True,
    'num_workers': 16,
    'compute_ci': True,
    'n_bootstraps': 2000,
    'metrics': ['accuracy', 'auc', 'macro_f1', 'weighted_f1']
}

# k折交叉验证配置
KFOLD_CONFIG = {
    'models': ['CLIP', 'SigLIP'],
    'datasets': ['Herlev', 'FNAC2019'],
    'batch_size': 64,
    'epochs': 50,
    'lr': 1e-3,
    'gpu': 0,
    'kfold': True,
    'k': 5,
    'compute_ci': True,
    'n_bootstraps': 1000,
    'metrics': ['accuracy', 'auc', 'macro_f1']
}

# 完整评估配置 - 评估所有模型和数据集
FULL_EVAL_CONFIG = {
    'models': ['ResNet50', 'ViT-L', 'CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M'],
    'datasets': ['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC'],
    'batch_size': 64,
    'epochs': 50,
    'lr': 1e-3,
    'gpu': 0,
    'cache_data': True,
    'num_workers': 8,
    'compute_ci': True,
    'n_bootstraps': 1000,
    'metrics': ['accuracy', 'auc', 'macro_f1', 'weighted_f1']
}

# 内存受限配置 - 适用于内存较小的环境
MEMORY_LIMITED_CONFIG = {
    'models': ['ResNet50', 'CLIP'],
    'datasets': ['Herlev', 'FNAC2019'],
    'batch_size': 16,
    'epochs': 30,
    'lr': 1e-3,
    'gpu': 0,
    'cache_data': False,
    'num_workers': 2,
    'compute_ci': False,
    'disable_progress_bar': True
}

# ========================================
# 配置生成函数
# ========================================

def generate_command_line(config_name, config_dict):
    """
    根据配置字典生成命令行参数字符串
    Generate command line arguments string from configuration dictionary
    """
    cmd_parts = ['python main.py']
    
    for key, value in config_dict.items():
        if isinstance(value, bool):
            if value:  # 只有当值为True时才添加标志
                cmd_parts.append(f'--{key}')
        elif isinstance(value, list):
            cmd_parts.append(f'--{key}')
            cmd_parts.extend(map(str, value))
        else:
            cmd_parts.append(f'--{key} {value}')
    
    return ' \\\n    '.join(cmd_parts)

def print_all_configs():
    """打印所有预定义配置的命令行形式"""
    configs = {
        'QUICK_TEST': QUICK_TEST_CONFIG,
        'STANDARD_EVAL': STANDARD_EVAL_CONFIG,
        'HIGH_PERFORMANCE': HIGH_PERFORMANCE_CONFIG,
        'KFOLD': KFOLD_CONFIG,
        'FULL_EVAL': FULL_EVAL_CONFIG,
        'MEMORY_LIMITED': MEMORY_LIMITED_CONFIG
    }
    
    print("=" * 80)
    print("预定义配置模板 (Predefined Configuration Templates)")
    print("=" * 80)
    
    for config_name, config_dict in configs.items():
        print(f"\n# {config_name} 配置")
        print(f"# {config_name} Configuration")
        print("-" * 60)
        print(generate_command_line(config_name, config_dict))
        print()

def get_config(config_name):
    """
    获取指定名称的配置
    Get configuration by name
    """
    configs = {
        'quick_test': QUICK_TEST_CONFIG,
        'standard': STANDARD_EVAL_CONFIG,
        'high_performance': HIGH_PERFORMANCE_CONFIG,
        'kfold': KFOLD_CONFIG,
        'full': FULL_EVAL_CONFIG,
        'memory_limited': MEMORY_LIMITED_CONFIG
    }
    
    return configs.get(config_name.lower())

# ========================================
# 自定义配置构建器
# ========================================

class ConfigBuilder:
    """配置构建器类，用于动态创建配置"""
    
    def __init__(self):
        self.config = {}
    
    def models(self, *models):
        """设置模型列表"""
        self.config['models'] = list(models)
        return self
    
    def datasets(self, *datasets):
        """设置数据集列表"""
        self.config['datasets'] = list(datasets)
        return self
    
    def training(self, batch_size=64, epochs=50, lr=1e-3):
        """设置训练参数"""
        self.config.update({
            'batch_size': batch_size,
            'epochs': epochs,
            'lr': lr
        })
        return self
    
    def hardware(self, gpu=0, cache_data=False, num_workers=None):
        """设置硬件参数"""
        self.config['gpu'] = gpu
        if cache_data:
            self.config['cache_data'] = True
        if num_workers is not None:
            self.config['num_workers'] = num_workers
        return self
    
    def evaluation(self, compute_ci=True, n_bootstraps=1000, metrics=None):
        """设置评估参数"""
        if compute_ci:
            self.config['compute_ci'] = True
            self.config['n_bootstraps'] = n_bootstraps
        if metrics:
            self.config['metrics'] = metrics
        return self
    
    def kfold(self, k=5):
        """启用k折交叉验证"""
        self.config['kfold'] = True
        self.config['k'] = k
        return self
    
    def build(self):
        """构建最终配置"""
        return self.config.copy()
    
    def to_command_line(self):
        """转换为命令行字符串"""
        return generate_command_line("custom", self.config)

# ========================================
# 使用示例
# ========================================

if __name__ == '__main__':
    # 打印所有预定义配置
    print_all_configs()
    
    print("\n" + "=" * 80)
    print("自定义配置示例 (Custom Configuration Example)")
    print("=" * 80)
    
    # 使用配置构建器创建自定义配置
    custom_config = (ConfigBuilder()
                    .models('CLIP', 'SigLIP')
                    .datasets('Herlev', 'FNAC2019')
                    .training(batch_size=32, epochs=30, lr=5e-4)
                    .hardware(gpu=1, cache_data=True, num_workers=8)
                    .evaluation(compute_ci=True, n_bootstraps=500, metrics=['accuracy', 'auc'])
                    .build())
    
    print("\n# 自定义配置")
    print("# Custom Configuration")
    print("-" * 60)
    print(generate_command_line("custom", custom_config))
