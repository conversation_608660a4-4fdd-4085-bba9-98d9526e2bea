# 参数整理说明 (Parameter Organization Guide)

本项目已将所有参数按功能进行了重新整理和分类，使其更易于理解和使用。

## 📁 文件结构

```
├── main.py                 # 主程序（已重新整理参数）
├── PARAMETERS_GUIDE.md     # 详细参数说明文档
├── config_templates.py     # 预定义配置模板
├── run_config.py          # 快速运行脚本
└── README_PARAMETERS.md   # 本说明文件
```

## 🔧 参数分类

### 1. 模型和数据集选择参数
- `--models`: 选择要评估的模型
- `--datasets`: 选择要评估的数据集

### 2. 训练超参数
- `--batch_size`: 批次大小
- `--epochs`: 训练轮数
- `--lr`: 学习率

### 3. 硬件和性能参数
- `--gpu`: GPU设备ID
- `--cache_data`: 内存缓存
- `--num_workers`: 工作线程数
- `--disable_progress_bar`: 禁用进度条

### 4. 评估和验证参数
- `--kfold`: k折交叉验证
- `--k`: 折数
- `--compute_ci`: 计算置信区间
- `--n_bootstraps`: bootstrap采样次数

### 5. 输出和报告参数
- `--metrics`: 评估指标
- `--force_recompute`: 强制重新计算

## 🚀 快速开始

### 方法1: 使用预定义配置（推荐）

```bash
# 查看所有可用配置
python run_config.py --list

# 快速测试
python run_config.py quick_test

# 标准评估
python run_config.py standard

# 高性能评估
python run_config.py high_performance

# k折交叉验证
python run_config.py kfold

# 完整评估（所有模型和数据集）
python run_config.py full
```

### 方法2: 直接使用main.py

```bash
# 基本使用
python main.py --models CLIP SigLIP --datasets Herlev FNAC2019

# 高性能配置
python main.py --cache_data --num_workers 16 --batch_size 128 --gpu 0

# k折交叉验证
python main.py --kfold --k 5 --models CLIP --datasets Herlev
```

### 方法3: 使用配置模板生成命令

```python
from config_templates import ConfigBuilder

# 创建自定义配置
config = (ConfigBuilder()
          .models('CLIP', 'SigLIP')
          .datasets('Herlev', 'FNAC2019')
          .training(batch_size=32, epochs=30)
          .hardware(gpu=1, cache_data=True)
          .evaluation(compute_ci=True)
          .to_command_line())

print(config)
```

## 📖 详细文档

- **完整参数说明**: 查看 `PARAMETERS_GUIDE.md`
- **配置模板**: 查看 `config_templates.py`
- **快速运行**: 使用 `run_config.py`

## 💡 使用建议

### 初次使用
```bash
python run_config.py quick_test
```

### 正式评估
```bash
python run_config.py standard --gpu 0
```

### 资源充足时
```bash
python run_config.py high_performance
```

### 内存受限时
```bash
python run_config.py memory_limited
```

## 🔍 参数覆盖

可以在使用预定义配置时覆盖特定参数：

```bash
# 使用标准配置但改变GPU和批次大小
python run_config.py standard --gpu 1 --batch-size 32

# 干运行（只显示命令不执行）
python run_config.py standard --dry-run
```

## ⚠️ 注意事项

1. **内存使用**: 启用 `--cache_data` 前确保有足够内存
2. **GPU选择**: 根据可用GPU调整 `--gpu` 参数
3. **批次大小**: 根据GPU内存调整 `--batch_size`
4. **计算时间**: k折交叉验证会显著增加运行时间

## 🆘 故障排除

### 内存不足
```bash
# 使用内存受限配置
python run_config.py memory_limited
```

### GPU内存不足
```bash
# 减小批次大小
python main.py --batch_size 16 --models CLIP --datasets Herlev
```

### 运行时间过长
```bash
# 使用快速测试配置
python run_config.py quick_test
```

## 📊 输出文件

运行后会在 `results/` 目录下生成：
- 指标文件 (`.txt`)
- CSV表格 (`.csv`)
- LaTeX表格 (`.tex`)

## 🔄 更新日志

- ✅ 重新整理了main.py中的参数分类
- ✅ 创建了详细的参数说明文档
- ✅ 提供了预定义配置模板
- ✅ 添加了快速运行脚本
- ✅ 支持参数覆盖和干运行模式
