# 参数使用指南 (Parameters Guide)

本文档详细说明了 `main.py` 脚本中所有参数的功能和用法，按功能分类整理。

## 📋 目录
- [模型和数据集选择参数](#模型和数据集选择参数)
- [训练超参数](#训练超参数)
- [硬件和性能参数](#硬件和性能参数)
- [评估和验证参数](#评估和验证参数)
- [输出和报告参数](#输出和报告参数)
- [使用示例](#使用示例)

---

## 🎯 模型和数据集选择参数

### `--models`
- **类型**: 列表参数 (nargs='+')
- **默认值**: `['ResNet50', 'ViT-L', 'CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M']`
- **功能**: 指定要评估的模型列表
- **可选模型**:
  - `ResNet50`: 传统CNN模型
  - `ViT-L`: Vision Transformer Large
  - `CLIP`: OpenAI CLIP模型
  - `SigLIP`: SigLIP模型
  - `SigLIP2-ViT-B`: SigLIP2 ViT-Base
  - `SigLIP2-ViT-L`: SigLIP2 ViT-Large
  - `SigLIP2-ViT-SO400M`: SigLIP2 ViT-SO400M
- **示例**: `--models CLIP SigLIP`

### `--datasets`
- **类型**: 列表参数 (nargs='+')
- **默认值**: `['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC']`
- **功能**: 指定要评估的数据集列表
- **可选数据集**:
  - `Herlev`: 宫颈细胞数据集
  - `HiCervix`: 高分辨率宫颈细胞数据集
  - `JinWooChoi`: 骨髓细胞数据集
  - `FNAC2019`: 乳腺细胞数据集
  - `LDCC`: 宫颈细胞数据集
  - `Sipakmed`: 宫颈细胞数据集
  - `Barcelona`: 骨髓细胞数据集
  - `BCI`: 血细胞图像数据集
  - `BCFCI`: 体腔液细胞数据集
  - `BMCC`: 骨髓细胞分类数据集
- **示例**: `--datasets Herlev FNAC2019`

---

## ⚙️ 训练超参数

### `--batch_size`
- **类型**: 整数
- **默认值**: `64`
- **功能**: 设置训练和评估时的批次大小
- **建议值**: 根据GPU内存调整 (16, 32, 64, 128)
- **示例**: `--batch_size 32`

### `--epochs`
- **类型**: 整数
- **默认值**: `50`
- **功能**: 设置训练轮数
- **建议值**: 根据数据集大小调整 (20-100)
- **示例**: `--epochs 100`

### `--lr`
- **类型**: 浮点数
- **默认值**: `1e-3` (0.001)
- **功能**: 设置优化器学习率
- **建议值**: 1e-4 到 1e-2 之间
- **示例**: `--lr 0.0005`

---

## 🖥️ 硬件和性能参数

### `--gpu`
- **类型**: 整数
- **默认值**: `7`
- **功能**: 指定使用的GPU设备ID
- **示例**: `--gpu 0`

### `--cache_data`
- **类型**: 布尔标志
- **默认值**: `False`
- **功能**: 将所有图像缓存到内存中以加速训练和评估
- **注意**: 需要足够的内存空间
- **示例**: `--cache_data`

### `--num_workers`
- **类型**: 整数
- **默认值**: `None` (自动设置为CPU核心数的一半)
- **功能**: 设置数据加载和缓存的工作线程数
- **建议值**: 根据CPU核心数调整
- **示例**: `--num_workers 8`

### `--disable_progress_bar`
- **类型**: 布尔标志
- **默认值**: `False`
- **功能**: 禁用数据加载过程中的进度条显示
- **用途**: 适用于非交互式终端环境
- **示例**: `--disable_progress_bar`

---

## 📊 评估和验证参数

### `--kfold`
- **类型**: 布尔标志
- **默认值**: `False`
- **功能**: 使用k折交叉验证而不是官方的训练/验证/测试划分
- **示例**: `--kfold`

### `--k`
- **类型**: 整数
- **默认值**: `3`
- **功能**: k折交叉验证的折数
- **建议值**: 3, 5, 10
- **示例**: `--k 5`

### `--compute_ci`
- **类型**: 布尔标志
- **默认值**: `True`
- **功能**: 计算指标的bootstrap置信区间
- **示例**: `--compute_ci` (启用) 或不添加此参数来禁用

### `--n_bootstraps`
- **类型**: 整数
- **默认值**: `1000`
- **功能**: bootstrap采样的次数用于计算置信区间
- **建议值**: 500-2000
- **示例**: `--n_bootstraps 2000`

---

## 📈 输出和报告参数

### `--metrics`
- **类型**: 列表参数 (nargs='+')
- **默认值**: `['accuracy', 'auc', 'macro_f1']`
- **功能**: 指定用于表格生成的指标列表
- **可选指标**:
  - `accuracy`: 准确率
  - `auc`: AUC-ROC曲线下面积
  - `macro_f1`: 宏平均F1分数
  - `weighted_f1`: 加权F1分数
- **示例**: `--metrics accuracy auc macro_f1 weighted_f1`

### `--force_recompute`
- **类型**: 布尔标志
- **默认值**: `False`
- **功能**: 强制重新计算，即使结果文件已存在
- **示例**: `--force_recompute`

---

## 💡 使用示例

### 基本使用
```bash
python main.py --models CLIP SigLIP --datasets Herlev FNAC2019
```

### 高性能配置
```bash
python main.py --cache_data --num_workers 16 --batch_size 128 --gpu 0
```

### k折交叉验证
```bash
python main.py --kfold --k 5 --models CLIP --datasets Herlev
```

### 完整配置示例
```bash
python main.py \
    --models CLIP SigLIP SigLIP2-ViT-L \
    --datasets Herlev HiCervix FNAC2019 \
    --batch_size 64 \
    --epochs 50 \
    --lr 0.001 \
    --gpu 0 \
    --cache_data \
    --num_workers 8 \
    --kfold \
    --k 5 \
    --compute_ci \
    --n_bootstraps 1000 \
    --metrics accuracy auc macro_f1 weighted_f1 \
    --force_recompute
```

### 快速测试配置
```bash
python main.py \
    --models ResNet50 \
    --datasets Herlev \
    --epochs 10 \
    --batch_size 32 \
    --disable_progress_bar
```

---

## ⚠️ 注意事项

1. **内存使用**: 使用 `--cache_data` 时确保有足够的内存
2. **GPU内存**: 根据GPU内存调整 `--batch_size`
3. **计算时间**: k折交叉验证会显著增加计算时间
4. **文件覆盖**: 使用 `--force_recompute` 会覆盖现有结果文件
5. **参数组合**: 某些参数组合可能不兼容，请根据实际需求调整

---

## 📝 输出文件

脚本会生成以下类型的文件：
- `results/` 目录下的指标文件 (`.txt`)
- CSV格式的结果表格 (`.csv`)
- LaTeX格式的结果表格 (`.tex`)
