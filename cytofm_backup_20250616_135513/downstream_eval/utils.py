"""
Utilities for generating tables from evaluation results.
"""
import os
import csv
import numpy as np
import pandas as pd


def format_value_with_ci(value, ci_lower, ci_upper, precision=3):
    """
    Format a value with its confidence interval in the format: value (lower - upper)

    Args:
        value: The metric value
        ci_lower: Lower bound of confidence interval
        ci_upper: Upper bound of confidence interval
        precision: Number of decimal places

    Returns:
        formatted_str: Formatted string with value and CI
    """
    # Format with specified precision
    value_str = f"{value:.{precision}f}"
    lower_str = f"{ci_lower:.{precision}f}"
    upper_str = f"{ci_upper:.{precision}f}"

    # Format as value (lower - upper)
    return f"{value_str} ({lower_str} - {upper_str})"


def generate_csv_table(results, output_path, metric='accuracy'):
    """
    Generate a CSV table from evaluation results

    Args:
        results: Dictionary with structure {model: {dataset: metrics}}
        output_path: Path to save the CSV file
        metric: Which metric to include in the table (default: accuracy)

    Returns:
        df: Pandas DataFrame containing the table
    """
    # Create a DataFrame to store the results
    models = list(results.keys())
    datasets = list(results[models[0]].keys())

    # Initialize DataFrame with datasets as columns and models as rows
    df = pd.DataFrame(index=models, columns=datasets)

    # Fill the DataFrame with results
    for model in models:
        for dataset in datasets:
            metrics = results[model][dataset]

            # Check if confidence intervals are available
            if f"{metric}_ci" in metrics:
                value = metrics[metric] / 100.0  # Convert percentage to decimal
                ci_lower, ci_upper = metrics[f"{metric}_ci"]
                ci_lower /= 100.0  # Convert percentage to decimal
                ci_upper /= 100.0  # Convert percentage to decimal

                # Format value with CI
                df.loc[model, dataset] = format_value_with_ci(value, ci_lower, ci_upper)
            else:
                # Format value without CI
                value = metrics[metric] / 100.0  # Convert percentage to decimal
                df.loc[model, dataset] = f"{value:.3f}"

    # Save to CSV
    df.to_csv(output_path)
    print(f"CSV table saved to {output_path}")

    return df


def generate_latex_table(results, output_path, metric='accuracy', caption="", label=""):
    """
    Generate a LaTeX table from evaluation results

    Args:
        results: Dictionary with structure {model: {dataset: metrics}}
        output_path: Path to save the LaTeX file
        metric: Which metric to include in the table (default: accuracy)
        caption: Table caption
        label: Table label for referencing

    Returns:
        latex_str: String containing the LaTeX table
    """
    # Create a DataFrame to store the results
    models = list(results.keys())
    datasets = list(results[models[0]].keys())

    # Initialize DataFrame with datasets as columns and models as rows
    df = pd.DataFrame(index=models, columns=datasets)

    # Fill the DataFrame with results and track best values
    best_values = {dataset: 0.0 for dataset in datasets}
    best_models = {dataset: None for dataset in datasets}

    for model in models:
        for dataset in datasets:
            metrics = results[model][dataset]

            # Get the metric value
            value = metrics[metric] / 100.0  # Convert percentage to decimal

            # Track best value for each dataset
            if value > best_values[dataset]:
                best_values[dataset] = value
                best_models[dataset] = model

            # Check if confidence intervals are available
            if f"{metric}_ci" in metrics:
                ci_lower, ci_upper = metrics[f"{metric}_ci"]
                ci_lower /= 100.0  # Convert percentage to decimal
                ci_upper /= 100.0  # Convert percentage to decimal

                # Format value with CI
                df.loc[model, dataset] = format_value_with_ci(value, ci_lower, ci_upper)
            else:
                # Format value without CI
                df.loc[model, dataset] = f"{value:.3f}"

    # Begin LaTeX table
    latex_str = "\\begin{table}[ht]\n"
    latex_str += "\\centering\n"
    latex_str += "\\caption{" + caption + "}\n"
    latex_str += "\\label{" + label + "}\n"

    # Table header
    latex_str += "\\begin{tabular}{l" + "c" * len(datasets) + "}\n"
    latex_str += "\\hline\n"
    latex_str += "Pretrain settings & " + " & ".join(datasets) + " \\\\\n"
    latex_str += "\\hline\n"

    # Table body
    for model in models:
        row = model + " & "
        for dataset in datasets:
            value = df.loc[model, dataset]

            # Bold the best result for each dataset
            if model == best_models[dataset]:
                value = "\\textbf{" + value + "}"

            row += value + " & "

        # Remove the last " & " and add line end
        row = row[:-3] + " \\\\\n"
        latex_str += row

    # Table footer
    latex_str += "\\hline\n"
    latex_str += "\\end{tabular}\n"
    latex_str += "\\end{table}"

    # Save to file
    with open(output_path, 'w') as f:
        f.write(latex_str)

    print(f"LaTeX table saved to {output_path}")

    return latex_str


def collect_results(model_results):
    """
    Collect and organize results from multiple model-dataset evaluations

    Args:
        model_results: List of tuples (model_name, dataset_name, metrics)

    Returns:
        results: Dictionary with structure {model: {dataset: metrics}}
    """
    results = {}

    for model_name, dataset_name, metrics in model_results:
        # Initialize model entry if not exists
        if model_name not in results:
            results[model_name] = {}

        # Store metrics for this model-dataset combination
        results[model_name][dataset_name] = metrics

    return results


def generate_combined_table(results, csv_path, latex_path, metrics=['accuracy'], compute_ci=True, caption="", label=""):
    """
    Generate a combined table with multiple metrics

    Args:
        results: Dictionary with structure {model: {dataset: metrics}}
        csv_path: Path to save the CSV file
        latex_path: Path to save the LaTeX file
        metrics: List of metrics to include in the table
        compute_ci: Whether to include confidence intervals
        caption: Table caption
        label: Table label for referencing

    Returns:
        combined_df: Pandas DataFrame containing the combined table
    """
    # Create a DataFrame to store the combined results
    models = list(results.keys())
    datasets = list(results[models[0]].keys())

    # Create a multi-level column index for the combined table
    # Level 1: Dataset, Level 2: Metric
    column_tuples = [(dataset, metric) for dataset in datasets for metric in metrics]
    multi_index = pd.MultiIndex.from_tuples(column_tuples, names=['Dataset', 'Metric'])

    # Initialize DataFrame with multi-level columns and models as rows
    combined_df = pd.DataFrame(index=models, columns=multi_index)

    # Fill the DataFrame with results and track best values
    best_values = {(dataset, metric): 0.0 for dataset in datasets for metric in metrics}
    best_models = {(dataset, metric): None for dataset in datasets for metric in metrics}

    for model in models:
        for dataset in datasets:
            for metric in metrics:
                if dataset in results[model] and metric in results[model][dataset]:
                    metrics_data = results[model][dataset]

                    # Get the metric value
                    value = metrics_data[metric] / 100.0  # Convert percentage to decimal

                    # Track best value for each dataset-metric combination
                    if value > best_values[(dataset, metric)]:
                        best_values[(dataset, metric)] = value
                        best_models[(dataset, metric)] = model

                    # Check if confidence intervals are available
                    if compute_ci and f"{metric}_ci" in metrics_data:
                        ci_lower, ci_upper = metrics_data[f"{metric}_ci"]
                        ci_lower /= 100.0  # Convert percentage to decimal
                        ci_upper /= 100.0  # Convert percentage to decimal

                        # Format value with CI
                        combined_df.loc[model, (dataset, metric)] = format_value_with_ci(value, ci_lower, ci_upper)
                    else:
                        # Format value without CI
                        combined_df.loc[model, (dataset, metric)] = f"{value:.3f}"

    # Save to CSV
    combined_df.to_csv(csv_path)
    print(f"Combined CSV table saved to {csv_path}")

    # Generate LaTeX table
    latex_str = generate_combined_latex_table(combined_df, best_models, metrics, datasets, caption, label)

    # Save LaTeX to file
    with open(latex_path, 'w') as f:
        f.write(latex_str)

    print(f"Combined LaTeX table saved to {latex_path}")

    return combined_df


def generate_combined_latex_table(df, best_models, metrics, datasets, caption="", label=""):
    """
    Generate a LaTeX table from a combined DataFrame with multiple metrics

    Args:
        df: DataFrame with multi-level columns (dataset, metric)
        best_models: Dictionary mapping (dataset, metric) to the best model
        metrics: List of metrics included in the table
        datasets: List of datasets included in the table
        caption: Table caption
        label: Table label for referencing

    Returns:
        latex_str: String containing the LaTeX table
    """
    # Begin LaTeX table with required package comments
    latex_str = "% Requires packages: multirow, graphicx\n"
    latex_str += "% \\usepackage{multirow}\n"
    latex_str += "% \\usepackage{graphicx}\n\n"
    latex_str += "\\begin{table}[ht]\n"
    latex_str += "\\centering\n"
    latex_str += "\\caption{" + caption + "}\n"
    latex_str += "\\label{" + label + "}\n"

    # Use resizebox to fit the table to the page width
    latex_str += f"\\resizebox{{\textwidth}}{{!}}{{%\n"

    # Table header with multi-column for each dataset
    latex_str += "\\begin{tabular}{l" + "c" * (len(datasets) * len(metrics)) + "}\n"
    latex_str += "\\hline\n"

    # First row: Dataset names spanning multiple columns
    latex_str += "\\multirow{2}{*}{Pretrain settings}"
    for dataset in datasets:
        latex_str += f" & \\multicolumn{{{len(metrics)}}}{{c}}{{{dataset}}}"
    latex_str += " \\\\\n"

    # Second row: Metric names
    latex_str += " "
    for _ in datasets:
        for metric in metrics:
            latex_str += f" & {metric.capitalize()}"
    latex_str += " \\\\\n"
    latex_str += "\\hline\n"

    # Table body
    for model in df.index:
        row = model
        for dataset in datasets:
            for metric in metrics:
                value = df.loc[model, (dataset, metric)]

                # Bold the best result for each dataset-metric combination
                if model == best_models.get((dataset, metric)):
                    value = "\\textbf{" + value + "}"

                row += f" & {value}"

        row += " \\\\\n"
        latex_str += row

    # Table footer
    latex_str += "\\hline\n"
    latex_str += "\\end{tabular}\n"
    latex_str += "}\n"  # End of resizebox
    latex_str += "\\end{table}"

    return latex_str
