# Example custom configuration file for linear probing experiments
# This file shows how to customize different parameters for specific experiments

# Model Configuration
model_config:
  # Evaluate only specific models
  models:
    - "CLIP"
    - "SigLIP"
  
  # Use different GPU
  gpu: 0

# Dataset Configuration
dataset_config:
  # Evaluate on subset of datasets
  datasets:
    - "Herlev"
    - "HiCervix"
    - "FNAC2019"
  
  # Use larger batch size for faster training
  batch_size: 128
  
  # Enable data caching for faster training
  cache_data: true
  
  # Use more workers for data loading
  num_workers: 8
  
  # Disable progress bar for cleaner output in logs
  disable_progress_bar: true

# Training Configuration
training_config:
  # Use fewer epochs for quick testing
  epochs: 20
  
  # Use higher learning rate
  lr: 0.01

# Evaluation Configuration
evaluation_config:
  # Disable confidence intervals for faster evaluation
  compute_ci: false
  
  # Fewer bootstrap samples if CI is enabled
  n_bootstraps: 500
  
  # Evaluate only accuracy metric
  metrics:
    - "accuracy"
  
  # Force recomputation of all results
  force_recompute: true
  
  # Use k-fold cross-validation
  kfold: true
  
  # Use 5-fold cross-validation
  k: 5

# Output Configuration
output_config:
  # Results directory (relative to project root)
  results_dir: "results"
