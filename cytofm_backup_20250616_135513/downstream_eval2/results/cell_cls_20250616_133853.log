/home/<USER>/anaconda3/envs/jh-dino/lib/python3.9/site-packages/torch/_utils.py:776: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()
  return self.fget.__get__(instance, owner)()
/home/<USER>/anaconda3/envs/jh-dino/lib/python3.9/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/jh-dino/lib/python3.9/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet50_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet50_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
Using device: cuda:4

Training:   0%|          | 0/11 [00:00<?, ?it/s]
Training:   9%|▉         | 1/11 [00:03<00:39,  3.91s/it]
Training:  27%|██▋       | 3/11 [00:04<00:08,  1.09s/it]
Training:  45%|████▌     | 5/11 [00:04<00:03,  1.75it/s]
Training:  64%|██████▎   | 7/11 [00:04<00:01,  2.76it/s]
Training:  82%|████████▏ | 9/11 [00:04<00:00,  3.83it/s]
Training: 100%|██████████| 11/11 [00:04<00:00,  5.21it/s]
Training: 100%|██████████| 11/11 [00:04<00:00,  2.29it/s]
Epoch [1/5], Train Loss: 1.7963, Train Acc: 31.20%, Val Loss: 1.5769, Val Acc: 43.96%

Training:   0%|          | 0/11 [00:00<?, ?it/s]
Training:   9%|▉         | 1/11 [00:00<00:07,  1.34it/s]
Training:  18%|█▊        | 2/11 [00:00<00:03,  2.63it/s]
Training:  36%|███▋      | 4/11 [00:01<00:01,  5.46it/s]
Training:  55%|█████▍    | 6/11 [00:01<00:00,  6.86it/s]
Training:  73%|███████▎  | 8/11 [00:01<00:00,  7.94it/s]
Training:  91%|█████████ | 10/11 [00:01<00:00,  9.70it/s]
Training: 100%|██████████| 11/11 [00:01<00:00,  6.66it/s]
Epoch [2/5], Train Loss: 1.4433, Train Acc: 39.47%, Val Loss: 1.4171, Val Acc: 50.55%

Training:   0%|          | 0/11 [00:00<?, ?it/s]
Training:   9%|▉         | 1/11 [00:00<00:06,  1.59it/s]
Training:  27%|██▋       | 3/11 [00:00<00:01,  4.53it/s]
Training:  45%|████▌     | 5/11 [00:00<00:00,  7.17it/s]
Training:  64%|██████▎   | 7/11 [00:01<00:00,  9.40it/s]
Training:  82%|████████▏ | 9/11 [00:01<00:00, 11.25it/s]
Training: 100%|██████████| 11/11 [00:01<00:00,  8.19it/s]
Epoch [3/5], Train Loss: 1.2106, Train Acc: 56.16%, Val Loss: 1.2227, Val Acc: 54.95%

Training:   0%|          | 0/11 [00:00<?, ?it/s]
Training:   9%|▉         | 1/11 [00:00<00:06,  1.53it/s]
Training:  18%|█▊        | 2/11 [00:00<00:02,  3.04it/s]
Training:  36%|███▋      | 4/11 [00:00<00:01,  5.75it/s]
Training:  55%|█████▍    | 6/11 [00:01<00:00,  8.32it/s]
Training:  73%|███████▎  | 8/11 [00:01<00:00, 10.42it/s]
Training:  91%|█████████ | 10/11 [00:01<00:00, 12.04it/s]
Training: 100%|██████████| 11/11 [00:01<00:00,  7.89it/s]
Epoch [4/5], Train Loss: 1.1193, Train Acc: 58.03%, Val Loss: 1.2265, Val Acc: 56.04%

Training:   0%|          | 0/11 [00:00<?, ?it/s]
Training:   9%|▉         | 1/11 [00:00<00:06,  1.65it/s]
Training:  27%|██▋       | 3/11 [00:00<00:01,  4.82it/s]
Training:  45%|████▌     | 5/11 [00:00<00:00,  6.86it/s]
Training:  64%|██████▎   | 7/11 [00:01<00:00,  8.25it/s]
Training:  82%|████████▏ | 9/11 [00:01<00:00, 10.07it/s]
Training: 100%|██████████| 11/11 [00:01<00:00,  7.76it/s]
Epoch [5/5], Train Loss: 1.1328, Train Acc: 55.23%, Val Loss: 1.1929, Val Acc: 50.55%
Using best model with validation accuracy: 56.04%
Model saved to checkpoints/cell_cls/ResNet50_Herlev.pth

================================================================================
Evaluating model on test set...
Computing bootstrap confidence intervals: True
Number of bootstrap samples: 1000


Testing:   0%|          | 0/3 [00:00<?, ?it/s]
Testing:  33%|███▎      | 1/3 [00:00<00:01,  1.77it/s]
Testing: 100%|██████████| 3/3 [00:00<00:00,  5.25it/s]
Testing: 100%|██████████| 3/3 [00:00<00:00,  3.86it/s]
================================================================================
CLASSIFICATION METRICS
================================================================================

Overall Accuracy: 53.55% (95% CI: 46.45% - 60.11%)
AUC: 89.45% (95% CI: 83.86% - 94.09%)
Macro Precision: 58.87% (95% CI: 51.37% - 65.74%)
Macro Recall: 57.61% (95% CI: 50.31% - 64.60%)
Macro F1 Score: 57.83% (95% CI: 50.13% - 64.49%)
Weighted Precision: 55.90% (95% CI: 49.84% - 63.70%)
Weighted Recall: 53.55% (95% CI: 46.99% - 60.66%)
Weighted F1 Score: 54.22% (95% CI: 46.64% - 61.51%)

Per-class Metrics:
------------------------------------------------------------------------------------------------------------------------
Class                Accuracy                       Precision                      Recall                         F1 Score                      
------------------------------------------------------------------------------------------------------------------------
carcinoma_in_situ    53.33% (95% CI: 36.67%-70.00%) 59.26% (95% CI: 40.00%-78.12%) 53.33% (95% CI: 35.29%-70.97%) 56.14% (95% CI: 39.98%-70.18%)
light_dysplastic     55.88% (95% CI: 38.24%-73.53%) 79.17% (95% CI: 61.53%-95.24%) 55.88% (95% CI: 38.23%-72.42%) 65.52% (95% CI: 48.98%-78.43%)
moderate_dysplastic  45.16% (95% CI: 25.81%-64.52%) 36.84% (95% CI: 22.85%-51.35%) 45.16% (95% CI: 27.59%-62.97%) 40.58% (95% CI: 25.39%-54.80%)
normal_columnar      38.10% (95% CI: 19.05%-57.14%) 36.36% (95% CI: 16.00%-59.28%) 38.10% (95% CI: 16.67%-61.11%) 37.21% (95% CI: 19.35%-55.17%)
normal_intermediate  80.00% (95% CI: 60.00%-100.00%) 80.00% (95% CI: 57.14%-100.00%) 80.00% (95% CI: 54.53%-100.00%) 80.00% (95% CI: 59.26%-94.12%)
normal_superficiel   84.62% (95% CI: 61.54%-100.00%) 78.57% (95% CI: 54.55%-100.00%) 84.62% (95% CI: 62.50%-100.00%) 81.48% (95% CI: 59.98%-95.24%)
severe_dysplastic    46.15% (95% CI: 30.77%-61.54%) 41.86% (95% CI: 26.92%-57.14%) 46.15% (95% CI: 31.25%-62.16%) 43.90% (95% CI: 30.14%-56.10%)

Class Support (number of samples):
--------------------------------------------------
carcinoma_in_situ    30
light_dysplastic     34
moderate_dysplastic  31
normal_columnar      21
normal_intermediate  15
normal_superficiel   13
severe_dysplastic    39

Confusion Matrix:
          0     1     2     3     4     5     6
-----------------------------------------------
    0    <USER>     <GROUP>     3     4     0     0     7
    1     1    19    11     1     1     0     1
    2     3     4    14     2     0     0     8
    3     0     1     3     8     0     0     9
    4     0     0     0     0    12     3     0
    5     0     0     0     0     2    11     0
    6     7     0     7     7     0     0    18

Results saved to results/cell_cls/ResNet50_Herlev_with_ci_metrics.txt
CSV table saved to results/table_accuracy_with_ci_20250616-134015.csv
LaTeX table saved to results/table_accuracy_with_ci_20250616-134015.tex
CSV table saved to results/table_auc_with_ci_20250616-134015.csv
LaTeX table saved to results/table_auc_with_ci_20250616-134015.tex
CSV table saved to results/table_macro_f1_with_ci_20250616-134015.csv
LaTeX table saved to results/table_macro_f1_with_ci_20250616-134015.tex
Combined CSV table saved to results/table_combined_with_ci_20250616-134015.csv
Combined LaTeX table saved to results/table_combined_with_ci_20250616-134015.tex

Results tables generated:
Individual metric tables:
  accuracy: CSV - results/table_accuracy_with_ci_20250616-134015.csv, LaTeX - results/table_accuracy_with_ci_20250616-134015.tex
  auc: CSV - results/table_auc_with_ci_20250616-134015.csv, LaTeX - results/table_auc_with_ci_20250616-134015.tex
  macro_f1: CSV - results/table_macro_f1_with_ci_20250616-134015.csv, LaTeX - results/table_macro_f1_with_ci_20250616-134015.tex

Combined tables with all metrics:
  CSV: results/table_combined_with_ci_20250616-134015.csv
  LaTeX: results/table_combined_with_ci_20250616-134015.tex
