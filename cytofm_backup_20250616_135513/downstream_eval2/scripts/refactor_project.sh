#!/bin/bash

# <PERSON><PERSON>t to refactor and organize the project structure
# This script will create a more organized directory structure
# and move files to their appropriate locations

# Create a backup of the current project
echo "Creating backup of the current project..."
BACKUP_DIR="../cytofm_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r ../* "$BACKUP_DIR"
echo "Backup created at $BACKUP_DIR"

# Standardize directory structure
echo "Standardizing directory structure..."

# Create standard directories for each task
TASKS="cell_cls cell_det cell_seg WSI_cls others"
COMPONENTS="models datasets utils evaluation configs"

for task in $TASKS; do
    for component in $COMPONENTS; do
        mkdir -p "src/$task/$component"
        touch "src/$task/$component/__init__.py"
    done
    touch "src/$task/__init__.py"
done

# Create common directories
mkdir -p "src/common/utils"
mkdir -p "src/common/models"
mkdir -p "src/common/evaluation"
touch "src/common/__init__.py"
touch "src/common/utils/__init__.py"
touch "src/common/models/__init__.py"
touch "src/common/evaluation/__init__.py"

# Create directories for scripts and tools
mkdir -p "scripts"
mkdir -p "tools"

# Create directories for results and checkpoints
mkdir -p "results"
mkdir -p "checkpoints"

echo "Directory structure standardized."