# Default configuration for cell classification

# Common settings
common:
  seed: 42
  gpu: 4
  num_workers: 4
  disable_progress_bar: false

# Data settings
data:
  dataset: "Herlev"  # Options: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, JinWooChoi, FNAC2019, LDCC, Sipakmed, Barcelona, BCI, BCFCI, BMCC
  root: "/jhcnas4/jh/cytology/CYTO_task"
  organ: "cervix"

# Model settings
model:
  feature_dim: 768  # Feature dimension from backbone

# Backbone settings
backbone:
  name: "ResNet50"  # Options: ResNet50, CLIP, SigLIP, SigLIP2-ViT-B, SigLIP2-ViT-L, SigLIP2-ViT-SO400M
  pretrained: true
  freeze: true

# Training settings
training:
  batch_size: 64
  epochs: 5
  lr: 0.001
  weight_decay: 0.0001
  optimizer: "Adam"
  scheduler: null

# Evaluation settings
evaluation:
  batch_size: 64
  compute_ci: true
  n_bootstraps: 1000
  metrics:
    - "accuracy"
    - "auc"
    - "macro_f1"

# Output settings
output:
  model_dir: "checkpoints/cell_cls"
  results_dir: "results/cell_cls"
