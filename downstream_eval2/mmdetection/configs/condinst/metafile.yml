Collections:
  - Name: CondInst
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x A100 GPUs
      Architecture:
        - FPN
        - FCOS
        - ResNet
    Paper: https://arxiv.org/abs/2003.05664
    README: configs/condinst/README.md

Models:
  - Name: condinst_r50_fpn_ms-poly-90k_coco_instance
    In Collection: CondInst
    Config: configs/condinst/condinst_r50_fpn_ms-poly-90k_coco_instance.py
    Metadata:
      Training Memory (GB): 4.4
      Iterations: 90000
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.0
    Weights: https://download.openmmlab.com/mmdetection/v3.0/condinst/condinst_r50_fpn_ms-poly-90k_coco_instance/condinst_r50_fpn_ms-poly-90k_coco_instance_20221129_125223-4c186406.pth
