Collections:
  - Name: ATSS
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - ATSS
        - FPN
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/1912.02424
      Title: 'Bridging the Gap Between Anchor-based and Anchor-free Detection via Adaptive Training Sample Selection'
    README: configs/atss/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/detectors/atss.py#L6
      Version: v2.0.0

Models:
  - Name: atss_r50_fpn_1x_coco
    In Collection: ATSS
    Config: configs/atss/atss_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 3.7
      inference time (ms/im):
        - value: 50.76
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/atss/atss_r50_fpn_1x_coco/atss_r50_fpn_1x_coco_20200209-985f7bd0.pth

  - Name: atss_r101_fpn_1x_coco
    In Collection: ATSS
    Config: configs/atss/atss_r101_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 5.6
      inference time (ms/im):
        - value: 81.3
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/atss/atss_r101_fpn_1x_coco/atss_r101_fpn_1x_20200825-dfcadd6f.pth
