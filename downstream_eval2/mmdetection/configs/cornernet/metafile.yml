Collections:
  - Name: CornerNet
    Metadata:
      Training Data: COCO
      Training Techniques:
        - Adam
      Training Resources: 8x V100 GPUs
      Architecture:
        - Corner Pooling
        - Stacked Hourglass Network
    Paper:
      URL: https://arxiv.org/abs/1808.01244
      Title: 'CornerNet: Detecting Objects as Paired Keypoints'
    README: configs/cornernet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.3.0/mmdet/models/detectors/cornernet.py#L9
      Version: v2.3.0

Models:
  - Name: cornernet_hourglass104_10xb5-crop511-210e-mstest_coco
    In Collection: CornerNet
    Config: configs/cornernet/cornernet_hourglass104_10xb5-crop511-210e-mstest_coco.py
    Metadata:
      Training Resources: 10x V100 GPUs
      Batch Size: 50
      Training Memory (GB): 13.9
      inference time (ms/im):
        - value: 238.1
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 210
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cornernet/cornernet_hourglass104_mstest_10x5_210e_coco/cornernet_hourglass104_mstest_10x5_210e_coco_20200824_185720-5fefbf1c.pth

  - Name: cornernet_hourglass104_8xb6-210e-mstest_coco
    In Collection: CornerNet
    Config: configs/cornernet/cornernet_hourglass104_8xb6-210e-mstest_coco.py
    Metadata:
      Batch Size: 48
      Training Memory (GB): 15.9
      inference time (ms/im):
        - value: 238.1
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 210
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cornernet/cornernet_hourglass104_mstest_8x6_210e_coco/cornernet_hourglass104_mstest_8x6_210e_coco_20200825_150618-79b44c30.pth

  - Name: cornernet_hourglass104_32xb3-210e-mstest_coco
    In Collection: CornerNet
    Config: configs/cornernet/cornernet_hourglass104_32xb3-210e-mstest_coco.py
    Metadata:
      Training Resources: 32x V100 GPUs
      Batch Size: 96
      Training Memory (GB): 9.5
      inference time (ms/im):
        - value: 256.41
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 210
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cornernet/cornernet_hourglass104_mstest_32x3_210e_coco/cornernet_hourglass104_mstest_32x3_210e_coco_20200819_203110-1efaea91.pth
