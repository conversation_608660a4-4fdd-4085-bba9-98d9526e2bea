Collections:
  - Name: GHM
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - GHM-C
        - GHM-R
        - FPN
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/1811.05181
      Title: 'Gradient Harmonized Single-stage Detector'
    README: configs/ghm/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/losses/ghm_loss.py#L21
      Version: v2.0.0

Models:
  - Name: retinanet_r50_fpn_ghm-1x_coco
    In Collection: GHM
    Config: configs/ghm/retinanet_r50_fpn_ghm-1x_coco.py
    Metadata:
      Training Memory (GB): 4.0
      inference time (ms/im):
        - value: 303.03
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 37.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/ghm/retinanet_ghm_r50_fpn_1x_coco/retinanet_ghm_r50_fpn_1x_coco_20200130-a437fda3.pth

  - Name: retinanet_r101_fpn_ghm-1x_coco
    In Collection: GHM
    Config: configs/ghm/retinanet_r101_fpn_ghm-1x_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 227.27
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/ghm/retinanet_ghm_r101_fpn_1x_coco/retinanet_ghm_r101_fpn_1x_coco_20200130-c148ee8f.pth

  - Name: retinanet_x101-32x4d_fpn_ghm-1x_coco
    In Collection: GHM
    Config: configs/ghm/retinanet_x101-32x4d_fpn_ghm-1x_coco.py
    Metadata:
      Training Memory (GB): 7.2
      inference time (ms/im):
        - value: 196.08
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/ghm/retinanet_ghm_x101_32x4d_fpn_1x_coco/retinanet_ghm_x101_32x4d_fpn_1x_coco_20200131-e4333bd0.pth

  - Name: retinanet_x101-64x4d_fpn_ghm-1x_coco
    In Collection: GHM
    Config: configs/ghm/retinanet_x101-64x4d_fpn_ghm-1x_coco.py
    Metadata:
      Training Memory (GB): 10.3
      inference time (ms/im):
        - value: 192.31
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/ghm/retinanet_ghm_x101_64x4d_fpn_1x_coco/retinanet_ghm_x101_64x4d_fpn_1x_coco_20200131-dd381cef.pth
