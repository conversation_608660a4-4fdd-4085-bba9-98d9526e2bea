Collections:
  - Name: Grounding DINO
    Metadata:
      Training Data: Objects365, GoldG, CC3M and COCO
      Training Techniques:
        - AdamW
        - Multi Scale Train
        - Gradient Clip
      Training Resources: 3090 GPUs
      Architecture:
        - Swin Transformer
        - BERT
    Paper:
      URL: https://arxiv.org/abs/2303.05499
      Title: 'Grounding DINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection
'
    README: configs/grounding_dino/README.md
    Code:
      URL:
      Version: v3.0.0

Models:
  - Name: grounding_dino_swin-t_pretrain_obj365_goldg_cap4m
    In Collection: Grounding DINO
    Config: configs/grounding_dino/grounding_dino_swin-t_pretrain_obj365_goldg_cap4m.py
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 48.5
    Weights: https://download.openmmlab.com/mmdetection/v3.0/grounding_dino/groundingdino_swint_ogc_mmdet-822d7e9d.pth
  - Name: grounding_dino_swin-b_pretrain_mixeddata
    In Collection: Grounding DINO
    Config: configs/grounding_dino/grounding_dino_swin-b_pretrain_mixeddata.py
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 56.9
    Weights: https://download.openmmlab.com/mmdetection/v3.0/grounding_dino/groundingdino_swinb_cogcoor_mmdet-55949c9c.pth
  - Name: grounding_dino_swin-t_finetune_16xb2_1x_coco
    In Collection: Grounding DINO
    Config: configs/grounding_dino/grounding_dino_swin-t_finetune_16xb2_1x_coco.py
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 58.1
    Weights: https://download.openmmlab.com/mmdetection/v3.0/grounding_dino/grounding_dino_swin-t_finetune_16xb2_1x_coco/grounding_dino_swin-t_finetune_16xb2_1x_coco_20230921_152544-5f234b20.pth
  - Name: grounding_dino_swin-b_finetune_16xb2_1x_coco
    In Collection: Grounding DINO
    Config: configs/grounding_dino/grounding_dino_swin-b_finetune_16xb2_1x_coco.py
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 59.7
    Weights: https://download.openmmlab.com/mmdetection/v3.0/grounding_dino/grounding_dino_swin-b_finetune_16xb2_1x_coco/grounding_dino_swin-b_finetune_16xb2_1x_coco_20230921_153201-f219e0c0.pth
  - Name: grounding_dino_r50_scratch_8xb2_1x_coco
    In Collection: Grounding DINO
    Config: configs/grounding_dino/grounding_dino_r50_scratch_8xb2_1x_coco.py
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 48.9
    Weights: https://download.openmmlab.com/mmdetection/v3.0/grounding_dino/grounding_dino_r50_scratch_8xb2_1x_coco/grounding_dino_r50_scratch_1x_coco-fe0002f2.pth
