Collections:
  - Name: BoxInst
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x A100 GPUs
      Architecture:
        - ResNet
        - FPN
        - CondInst
    Paper:
      URL: https://arxiv.org/abs/2012.02310
      Title: 'BoxInst: High-Performance Instance Segmentation with Box Annotations'
    README: configs/boxinst/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v3.0.0rc6/mmdet/models/detectors/boxinst.py#L8
      Version: v3.0.0rc6

Models:
  - Name: boxinst_r50_fpn_ms-90k_coco
    In Collection: BoxInst
    Config: configs/boxinst/boxinst_r50_fpn_ms-90k_coco.py
    Metadata:
      Iterations: 90000
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 30.8
    Weights: https://download.openmmlab.com/mmdetection/v3.0/boxinst/boxinst_r50_fpn_ms-90k_coco/boxinst_r50_fpn_ms-90k_coco_20221228_163052-6add751a.pth

  - Name: boxinst_r101_fpn_ms-90k_coco
    In Collection: BoxInst
    Config: configs/boxinst/boxinst_r101_fpn_ms-90k_coco.py
    Metadata:
      Iterations: 90000
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 32.7
    Weights: https://download.openmmlab.com/mmdetection/v3.0/boxinst/boxinst_r101_fpn_ms-90k_coco/boxinst_r101_fpn_ms-90k_coco_20221229_145106-facf375b.pth
