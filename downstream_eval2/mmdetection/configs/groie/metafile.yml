Collections:
  - Name: <PERSON>R<PERSON>IE
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Generic RoI Extractor
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: https://arxiv.org/abs/2004.13665
      Title: 'A novel Region of Interest Extraction Layer for Instance Segmentation'
    README: configs/groie/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.1.0/mmdet/models/roi_heads/roi_extractors/groie.py#L15
      Version: v2.1.0

Models:
  - Name: faster-rcnn_r50_fpn_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/faste-rcnn_r50_fpn_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/groie/faster_rcnn_r50_fpn_groie_1x_coco/faster_rcnn_r50_fpn_groie_1x_coco_20200604_211715-66ee9516.pth

  - Name: grid-rcnn_r50_fpn_gn-head-groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/grid-rcnn_r50_fpn_gn-head-groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/groie/grid_rcnn_r50_fpn_gn-head_groie_1x_coco/grid_rcnn_r50_fpn_gn-head_groie_1x_coco_20200605_202059-4b75d86f.pth

  - Name: mask-rcnn_r50_fpn_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask-rcnn_r50_fpn_groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/groie/mask_rcnn_r50_fpn_groie_1x_coco/mask_rcnn_r50_fpn_groie_1x_coco_20200604_211715-50d90c74.pth

  - Name: mask-rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask-rcnn_r50_fpn_syncbn-r4-gcb-c3-c5-groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:   37.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/groie/mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco/mask_rcnn_r50_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco_20200604_211715-42eb79e1.pth

  - Name: mask-rcnn_r101_fpn_syncbn-r4-gcb_c3-c5-groie_1x_coco
    In Collection: GRoIE
    Config: configs/groie/mask-rcnn_r101_fpn_syncbn-r4-gcb_c3-c5-groie_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/groie/mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco/mask_rcnn_r101_fpn_syncbn-backbone_r4_gcb_c3-c5_groie_1x_coco_20200607_224507-8daae01c.pth
