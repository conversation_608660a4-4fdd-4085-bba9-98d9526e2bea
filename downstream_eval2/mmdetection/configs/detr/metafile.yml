Collections:
  - Name: DETR
    Metadata:
      Training Data: COCO
      Training Techniques:
        - AdamW
        - Multi Scale Train
        - Gradient Clip
      Training Resources: 8x V100 GPUs
      Architecture:
        - ResNet
        - Transformer
    Paper:
      URL: https://arxiv.org/abs/2005.12872
      Title: 'End-to-End Object Detection with Transformers'
    README: configs/detr/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.7.0/mmdet/models/detectors/detr.py#L7
      Version: v2.7.0

Models:
  - Name: detr_r50_8xb2-150e_coco
    In Collection: DETR
    Config: configs/detr/detr_r50_8xb2-150e_coco.py
    Metadata:
      Training Memory (GB): 7.9
      Epochs: 150
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.9
    Weights: https://download.openmmlab.com/mmdetection/v3.0/detr/detr_r50_8xb2-150e_coco/detr_r50_8xb2-150e_coco_20221023_153551-436d03e8.pth
