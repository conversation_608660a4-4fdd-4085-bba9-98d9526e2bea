Collections:
  - Name: Guided Anchoring
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - Guided Anchoring
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/1901.03278
      Title: 'Region Proposal by Guided Anchoring'
    README: configs/guided_anchoring/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/dense_heads/ga_retina_head.py#L10
      Version: v2.0.0

Models:
  - Name: ga-rpn_r50-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-rpn_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 5.3
      inference time (ms/im):
        - value: 63.29
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Region Proposal
        Dataset: COCO
        Metrics:
          AR@1000: 68.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_rpn_r50_caffe_fpn_1x_coco/ga_rpn_r50_caffe_fpn_1x_coco_20200531-899008a6.pth

  - Name: ga-rpn_r101-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-rpn_r101-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.3
      inference time (ms/im):
        - value: 76.92
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Region Proposal
        Dataset: COCO
        Metrics:
          AR@1000: 69.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_rpn_r101_caffe_fpn_1x_coco/ga_rpn_r101_caffe_fpn_1x_coco_20200531-ca9ba8fb.pth

  - Name: ga-rpn_x101-32x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-rpn_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.5
      inference time (ms/im):
        - value: 100
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Region Proposal
        Dataset: COCO
        Metrics:
          AR@1000: 70.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_rpn_x101_32x4d_fpn_1x_coco/ga_rpn_x101_32x4d_fpn_1x_coco_20200220-c28d1b18.pth

  - Name: ga-rpn_x101-64x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-rpn_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.1
      inference time (ms/im):
        - value: 133.33
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Region Proposal
        Dataset: COCO
        Metrics:
          AR@1000: 70.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_rpn_x101_64x4d_fpn_1x_coco/ga_rpn_x101_64x4d_fpn_1x_coco_20200225-3c6e1aa2.pth

  - Name: ga-faster-rcnn_r50-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-faster-rcnn_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 5.5
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_faster_r50_caffe_fpn_1x_coco/ga_faster_r50_caffe_fpn_1x_coco_20200702_000718-a11ccfe6.pth

  - Name: ga-faster-rcnn_r101-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-faster-rcnn_r101-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.5
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_faster_r101_caffe_fpn_1x_coco/ga_faster_r101_caffe_fpn_1x_coco_bbox_mAP-0.415_20200505_115528-fb82e499.pth

  - Name: ga-faster-rcnn_x101-32x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-faster-rcnn_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.7
      inference time (ms/im):
        - value: 103.09
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_faster_x101_32x4d_fpn_1x_coco/ga_faster_x101_32x4d_fpn_1x_coco_20200215-1ded9da3.pth

  - Name: ga-faster-rcnn_x101-64x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-faster-rcnn_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 11.8
      inference time (ms/im):
        - value: 136.99
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_faster_x101_64x4d_fpn_1x_coco/ga_faster_x101_64x4d_fpn_1x_coco_20200215-0fa7bde7.pth

  - Name: ga-retinanet_r50-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-retinanet_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 3.5
      inference time (ms/im):
        - value: 59.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 36.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_retinanet_r50_caffe_fpn_1x_coco/ga_retinanet_r50_caffe_fpn_1x_coco_20201020-39581c6f.pth

  - Name: ga-retinanet_r101-caffe_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-retinanet_r101-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 5.5
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_retinanet_r101_caffe_fpn_1x_coco/ga_retinanet_r101_caffe_fpn_1x_coco_20200531-6266453c.pth

  - Name: ga-retinanet_x101-32x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-retinanet_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.9
      inference time (ms/im):
        - value: 94.34
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_retinanet_x101_32x4d_fpn_1x_coco/ga_retinanet_x101_32x4d_fpn_1x_coco_20200219-40c56caa.pth

  - Name: ga-retinanet_x101-64x4d_fpn_1x_coco
    In Collection: Guided Anchoring
    Config: configs/guided_anchoring/ga-retinanet_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 9.9
      inference time (ms/im):
        - value: 129.87
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/guided_anchoring/ga_retinanet_x101_64x4d_fpn_1x_coco/ga_retinanet_x101_64x4d_fpn_1x_coco_20200226-ef9f7f1f.pth
