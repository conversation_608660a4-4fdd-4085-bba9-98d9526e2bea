Collections:
  - Name: Weight Standardization
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Group Normalization
        - Weight Standardization
    Paper:
      URL: https://arxiv.org/abs/1903.10520
      Title: 'Weight Standardization'
    README: configs/gn+ws/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/configs/gn%2Bws/mask-rcnn_r50_fpn_gn-ws-all_2x_coco.py
      Version: v2.0.0

Models:
  - Name: faster-rcnn_r50_fpn_gn_ws-all_1x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/faster-rcnn_r50_fpn_gn-ws-all_1x_coco.py
    Metadata:
      Training Memory (GB): 5.9
      inference time (ms/im):
        - value: 85.47
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/faster_rcnn_r50_fpn_gn_ws-all_1x_coco/faster_rcnn_r50_fpn_gn_ws-all_1x_coco_20200130-613d9fe2.pth

  - Name: faster-rcnn_r101_fpn_gn-ws-all_1x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/faster-rcnn_r101_fpn_gn-ws-all_1x_coco.py
    Metadata:
      Training Memory (GB): 8.9
      inference time (ms/im):
        - value: 111.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/faster_rcnn_r101_fpn_gn_ws-all_1x_coco/faster_rcnn_r101_fpn_gn_ws-all_1x_coco_20200205-a93b0d75.pth

  - Name: faster-rcnn_x50-32x4d_fpn_gn-ws-all_1x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/faster-rcnn_x50-32x4d_fpn_gn-ws-all_1x_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 97.09
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/faster_rcnn_x50_32x4d_fpn_gn_ws-all_1x_coco/faster_rcnn_x50_32x4d_fpn_gn_ws-all_1x_coco_20200203-839c5d9d.pth

  - Name: faster-rcnn_x101-32x4d_fpn_gn-ws-all_1x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/faster-rcnn_x101-32x4d_fpn_gn-ws-all_1x_coco.py
    Metadata:
      Training Memory (GB): 10.8
      inference time (ms/im):
        - value: 131.58
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/faster_rcnn_x101_32x4d_fpn_gn_ws-all_1x_coco/faster_rcnn_x101_32x4d_fpn_gn_ws-all_1x_coco_20200212-27da1bc2.pth

  - Name: mask-rcnn_r50_fpn_gn_ws-all_2x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_r50_fpn_gn-ws-all_2x_coco.py
    Metadata:
      Training Memory (GB): 7.3
      inference time (ms/im):
        - value: 95.24
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  36.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_r50_fpn_gn_ws-all_2x_coco/mask_rcnn_r50_fpn_gn_ws-all_2x_coco_20200226-16acb762.pth

  - Name: mask-rcnn_r101_fpn_gn-ws-all_2x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_r101_fpn_gn-ws-all_2x_coco.py
    Metadata:
      Training Memory (GB): 10.3
      inference time (ms/im):
        - value: 116.28
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_r101_fpn_gn_ws-all_2x_coco/mask_rcnn_r101_fpn_gn_ws-all_2x_coco_20200212-ea357cd9.pth

  - Name: mask-rcnn_x50-32x4d_fpn_gn-ws-all_2x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_x50-32x4d_fpn_gn-ws-all_2x_coco.py
    Metadata:
      Training Memory (GB): 8.4
      inference time (ms/im):
        - value: 107.53
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_x50_32x4d_fpn_gn_ws-all_2x_coco/mask_rcnn_x50_32x4d_fpn_gn_ws-all_2x_coco_20200216-649fdb6f.pth

  - Name: mask-rcnn_x101-32x4d_fpn_gn-ws-all_2x_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_x101-32x4d_fpn_gn-ws-all_2x_coco.py
    Metadata:
      Training Memory (GB): 12.2
      inference time (ms/im):
        - value: 140.85
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_x101_32x4d_fpn_gn_ws-all_2x_coco/mask_rcnn_x101_32x4d_fpn_gn_ws-all_2x_coco_20200319-33fb95b5.pth

  - Name: mask-rcnn_r50_fpn_gn_ws-all_20_23_24e_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_r50_fpn_gn-ws-all_20-23-24e_coco.py
    Metadata:
      Training Memory (GB): 7.3
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_r50_fpn_gn_ws-all_20_23_24e_coco/mask_rcnn_r50_fpn_gn_ws-all_20_23_24e_coco_20200213-487d1283.pth

  - Name: mask-rcnn_r101_fpn_gn-ws-all_20-23-24e_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_r101_fpn_gn-ws-all_20-23-24e_coco.py
    Metadata:
      Training Memory (GB): 10.3
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  38.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_r101_fpn_gn_ws-all_20_23_24e_coco/mask_rcnn_r101_fpn_gn_ws-all_20_23_24e_coco_20200213-57b5a50f.pth

  - Name: mask-rcnn_x50-32x4d_fpn_gn-ws-all_20-23-24e_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_x50-32x4d_fpn_gn-ws-all_20-23-24e_coco.py
    Metadata:
      Training Memory (GB): 8.4
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  38.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_x50_32x4d_fpn_gn_ws-all_20_23_24e_coco/mask_rcnn_x50_32x4d_fpn_gn_ws-all_20_23_24e_coco_20200226-969bcb2c.pth

  - Name: mask-rcnn_x101-32x4d_fpn_gn-ws-all_20-23-24e_coco
    In Collection: Weight Standardization
    Config: configs/gn%2Bws/mask-rcnn_x101-32x4d_fpn_gn-ws-all_20-23-24e_coco.py
    Metadata:
      Training Memory (GB): 12.2
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  38.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/gn%2Bws/mask_rcnn_x101_32x4d_fpn_gn_ws-all_20_23_24e_coco/mask_rcnn_x101_32x4d_fpn_gn_ws-all_20_23_24e_coco_20200316-e6cd35ef.pth
