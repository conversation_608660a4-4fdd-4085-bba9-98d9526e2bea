configs/albu_example/mask-rcnn_r50_fpn_albu_1x_coco.py
configs/atss/atss_r50_fpn_1x_coco.py
configs/autoassign/autoassign_r50-caffe_fpn_1x_coco.py
configs/carafe/faster-rcnn_r50_fpn-carafe_1x_coco.py
configs/cascade_rcnn/cascade-rcnn_r50_fpn_1x_coco.py
configs/cascade_rcnn/cascade-mask-rcnn_r50_fpn_1x_coco.py
configs/cascade_rpn/cascade-rpn_faster-rcnn_r50-caffe_fpn_1x_coco.py
configs/centernet/centernet_r18-dcnv2_8xb16-crop512-140e_coco.py
configs/centernet/centernet-update_r50-caffe_fpn_ms-1x_coco.py
configs/centripetalnet/centripetalnet_hourglass104_16xb6-crop511-210e-mstest_coco.py
configs/cornernet/cornernet_hourglass104_8xb6-210e-mstest_coco.py
configs/convnext/mask-rcnn_convnext-t-p4-w7_fpn_amp-ms-crop-3x_coco.py
configs/dcn/faster-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
configs/dcnv2/faster-rcnn_r50_fpn_mdpool_1x_coco.py
configs/ddod/ddod_r50_fpn_1x_coco.py
configs/detectors/detectors_htc-r50_1x_coco.py
configs/deformable_detr/deformable-detr_r50_16xb2-50e_coco.py
configs/detr/detr_r50_8xb2-150e_coco.py
configs/double_heads/dh-faster-rcnn_r50_fpn_1x_coco.py
configs/dynamic_rcnn/dynamic-rcnn_r50_fpn_1x_coco.py
configs/dyhead/atss_r50_fpn_dyhead_1x_coco.py
configs/efficientnet/retinanet_effb3_fpn_8xb4-crop896-1x_coco.py
configs/empirical_attention/faster-rcnn_r50-attn1111_fpn_1x_coco.py
configs/faster_rcnn/faster-rcnn_r50_fpn_1x_coco.py
configs/faster_rcnn/faster-rcnn_r50-caffe-dc5_ms-1x_coco.py
configs/fcos/fcos_r50-caffe_fpn_gn-head-center-normbbox-centeronreg-giou_1x_coco.py
configs/foveabox/fovea_r50_fpn_gn-head-align_4xb4-2x_coco.py
configs/fpg/mask-rcnn_r50_fpg_crop640-50e_coco.py
configs/free_anchor/freeanchor_r50_fpn_1x_coco.py
configs/fsaf/fsaf_r50_fpn_1x_coco.py
configs/gcnet/mask-rcnn_r50-syncbn-gcb-r16-c3-c5_fpn_1x_coco.py
configs/gfl/gfl_r50_fpn_1x_coco.py
configs/ghm/retinanet_r50_fpn_ghm-1x_coco.py
configs/gn/mask-rcnn_r50_fpn_gn-all_2x_coco.py
configs/gn+ws/faster-rcnn_r50_fpn_gn-ws-all_1x_coco.py
configs/grid_rcnn/grid-rcnn_r50_fpn_gn-head_2x_coco.py
configs/groie/faste-rcnn_r50_fpn_groie_1x_coco.py
configs/guided_anchoring/ga-retinanet_r50-caffe_fpn_1x_coco.py
configs/hrnet/faster-rcnn_hrnetv2p-w18-1x_coco.py
configs/htc/htc_r50_fpn_1x_coco.py
configs/instaboost/mask-rcnn_r50_fpn_instaboost-4x_coco.py
configs/lad/lad_r50-paa-r101_fpn_2xb8_coco_1x.py
configs/ld/ld_r18-gflv1-r101_fpn_1x_coco.py
configs/libra_rcnn/libra-faster-rcnn_r50_fpn_1x_coco.py
configs/mask2former/mask2former_r50_8xb2-lsj-50e_coco-panoptic.py
configs/mask_rcnn/mask-rcnn_r50_fpn_1x_coco.py
configs/maskformer/maskformer_r50_ms-16xb1-75e_coco.py
configs/ms_rcnn/ms-rcnn_r50-caffe_fpn_1x_coco.py
configs/nas_fcos/nas-fcos_r50-caffe_fpn_nashead-gn-head_4xb4-1x_coco.py
configs/nas_fpn/retinanet_r50_nasfpn_crop640-50e_coco.py
configs/paa/paa_r50_fpn_1x_coco.py
configs/pafpn/faster-rcnn_r50_pafpn_1x_coco.py
configs/panoptic_fpn/panoptic-fpn_r50_fpn_1x_coco.py
configs/pisa/mask-rcnn_r50_fpn_pisa_1x_coco.py
configs/point_rend/point-rend_r50-caffe_fpn_ms-1x_coco.py
configs/pvt/retinanet_pvt-t_fpn_1x_coco.py
configs/queryinst/queryinst_r50_fpn_1x_coco.py
configs/regnet/retinanet_regnetx-800MF_fpn_1x_coco.py
configs/reppoints/reppoints-moment_r50_fpn_1x_coco.py
configs/res2net/faster-rcnn_res2net-101_fpn_2x_coco.py
configs/resnest/faster-rcnn_s50_fpn_syncbn-backbone+head_ms-range-1x_coco.py
configs/resnet_strikes_back/retinanet_r50-rsb-pre_fpn_1x_coco.py
configs/retinanet/retinanet_r50-caffe_fpn_1x_coco.py
configs/rpn/rpn_r50_fpn_1x_coco.py
configs/sabl/sabl-retinanet_r50_fpn_1x_coco.py
configs/scnet/scnet_r50_fpn_1x_coco.py
configs/scratch/faster-rcnn_r50-scratch_fpn_gn-all_6x_coco.py
configs/solo/solo_r50_fpn_1x_coco.py
configs/solov2/solov2_r50_fpn_1x_coco.py
configs/sparse_rcnn/sparse-rcnn_r50_fpn_1x_coco.py
configs/ssd/ssd300_coco.py
configs/ssd/ssdlite_mobilenetv2-scratch_8xb24-600e_coco.py
configs/swin/mask-rcnn_swin-t-p4-w7_fpn_1x_coco.py
configs/tood/tood_r50_fpn_1x_coco.py
'configs/tridentnet/tridentnet_r50-caffe_1x_coco.py
configs/vfnet/vfnet_r50_fpn_1x_coco.py
configs/yolact/yolact_r50_8xb8-55e_coco.py
configs/yolo/yolov3_d53_8xb8-320-273e_coco.py
configs/yolof/yolof_r50-c5_8xb8-1x_coco.py
configs/yolox/yolox_tiny_8xb8-300e_coco.py
