# Base configuration for cell classification

# Common settings
common:
  seed: 42
  gpu: 1
  num_workers: 4
  disable_progress_bar: false

# Data settings
data:
  dataset: "Herlev"  # Options: All-IDB, AML, Ascites2020, Barcelona, BCFC, BCI, BMC, BMT, Breast2023,
                     #          C_NMC_2019, CCS-Cell-Cls, CERVIX93, CSF2022, FNAC2019, <PERSON><PERSON>, HiCervix,
                     #          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LDCC, MendeleyLBC, PS3C, Raabin_WBC, RepoMedUNM, SIPaKMeD,
                     #          Thyroid2024, UFSC_OCPap

# Model settings
model:
  feature_dim: "auto"  # "auto" for auto-detection, or specify manually (e.g., 512, 768, 2048)
  dropout_p: 0.5       # Dropout probability for classifier (0.0-0.9)

# Backbone settings
backbone:
  name: "ResNet50"     # Options: ResNet18, ResNet34, ResNet50, ResNet101, ResNet152,
                       #          ViT-B-16, ViT-B-32, ViT-L-14, ViT-L-16, ViT-L-32, ViT-H-14,
                       #          CLIP, SigL<PERSON>, SigLIP2-ViT-B, SigLIP2-ViT-L, SigLIP2-ViT-SO400M,
                       #          dinov2_vitl, gpfm, CCS
  pretrained: true     # true/false
  freeze: true         # true for linear probing, false for fine-tuning

# Training settings
training:
  batch_size: 64       # Adjust based on GPU memory (16, 32, 64, 128)
  epochs: 50           # Training epochs (10-100)
  lr: 0.001            # Learning rate (0.00001-0.01, use lower for fine-tuning)
  weight_decay: 0.0001 # Weight decay (0.0001-0.001)
  optimizer: "Adam"    # Adam, SGD
  scheduler: null      # null, "StepLR", "CosineAnnealingLR"

# Evaluation settings
evaluation:
  batch_size: 64       # Evaluation batch size
  compute_ci: true     # Compute confidence intervals (true/false)
  n_bootstraps: 1000   # Bootstrap samples for CI (100-1000)

  # Cross validation (set cv_enabled: true to enable)
  cv_enabled: false    # Enable cross validation (true/false)
  cv_folds: 5          # Number of folds (3-10)
  cv_seed: 42          # Random seed for CV splits

  # Metrics to compute
  metrics:
    - "accuracy"
    - "auc"
    - "macro_f1"
    # Additional options: "macro_precision", "macro_recall", "weighted_f1"

# Output settings
output:
  model_dir: "/jhcnas2/home/<USER>/CARE/checkpoints/cell_cls/ccs/VITL_unfrozen"
  results_dir: "results/cell_cls/VITL_unfrozen"
