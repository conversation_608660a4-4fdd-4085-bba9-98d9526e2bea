import os
import numpy as np
import open_clip
import torch
import torchvision
import PIL.Image as Image
from torch.utils.data import DataLoader
import time
from tqdm import tqdm
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import gc

def build_model(model_name):
    available_models = ['ResNet50', 'CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M']
    ckpt_root = '/jhcnas2/shared_data/public/others/Cytology_VLP/ckpts'
    if model_name == 'ResNet50':
        _, _, preprocess = open_clip.create_model_and_transforms('ViT-L-14', pretrained=os.path.join(ckpt_root, '2025_04_21-15_22_04-model_ViT-L-14-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt')) # for API consistency
        model = torch.hub.load('pytorch/vision:v0.22.0', 'resnet50', pretrained=True)
    elif model_name == 'ViT-L':
        _, _, preprocess = open_clip.create_model_and_transforms('ViT-L-14', pretrained=os.path.join(ckpt_root, '2025_04_21-15_22_04-model_ViT-L-14-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt')) # for API consistency
        model = torch.hub.load('pytorch/vision:v0.22.0', 'vit_l_16', pretrained=True)
    elif model_name == 'CLIP':
        model, _, preprocess = open_clip.create_model_and_transforms('ViT-L-14', pretrained=os.path.join(ckpt_root, '2025_04_21-15_22_04-model_ViT-L-14-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt'))
    elif model_name == 'SigLIP':
        model, _, preprocess = open_clip.create_model_and_transforms('ViT-L-16-SigLIP-256', pretrained= os.path.join(ckpt_root, '2025_04_21-19_34_33-model_ViT-L-16-SigLIP-256-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt'))
    elif model_name == 'SigLIP2-ViT-B':
        model, _, preprocess = open_clip.create_model_and_transforms('hf-hub:timm/ViT-B-16-SigLIP2-256', pretrained=os.path.join(ckpt_root, '2025_04_21-12_25_13-model_hf-hub:timm-ViT-B-16-SigLIP2-256-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt'))
    elif model_name == 'SigLIP2-ViT-L':
        model, _, preprocess = open_clip.create_model_and_transforms('hf-hub:timm/ViT-L-16-SigLIP2-256', pretrained=os.path.join(ckpt_root, '2025_04_20-21_05_53-model_hf-hub:timm-ViT-L-16-SigLIP2-256-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt'))
    elif model_name == 'SigLIP2-ViT-SO400M':
        model, _, preprocess = open_clip.create_model_and_transforms('hf-hub:timm/ViT-SO400M-14-SigLIP2', pretrained=os.path.join(ckpt_root, '2025_04_17-11_32_17-model_hf-hub:timm-ViT-SO400M-14-SigLIP2-lr_0.0005-b_64-j_4-p_amp/checkpoints/epoch_40.pt'))
    elif model_name not in available_models:
        raise NotImplementedError
    return model, preprocess

def build_dataset(dataset_name, preprocess, use_cache=False, num_workers=None, disable_progress_bar=False):
    available_datasets = ['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC']
    dataset_root = '/jhcnas4/jh/cytology/CYTO_task'

    class CytoDataset(torch.utils.data.Dataset):
        def __init__(self, root, task_organ, task_type, preprocess, split='train'):
            self.root = root
            self.task_organ = task_organ
            self.task_type = task_type
            self.preprocess = preprocess
            self.image_paths = []
            self.labels = []
            self.label_dict = {}
            self.load_data(split)

        def load_data(self, split):
            if self.task_type == 'cls':
                # Load file paths and labels from split file
                split_file = os.path.join(self.root, f'{split}.txt')

                with open(split_file, 'r') as f:
                    lines = f.readlines()

                # Process all lines at once
                for line in lines:
                    # Split by any number of spaces
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        self.image_paths.append(os.path.join(self.root, parts[0]))
                        # The label is the last part after splitting
                        self.labels.append(parts[-1])
                    else:
                        print(f"Warning: Invalid line format in {split_file}: {line.strip()}")

            elif self.task_type == 'seg':
                pass
            elif self.task_type == 'det':
                pass
            else:
                raise NotImplementedError

            # Convert label strings to integers
            unique_labels = sorted(set(self.labels))
            self.label_dict = {label: i for i, label in enumerate(unique_labels)}

            print('{split} label_dict: '.format(split=split), self.label_dict)

            # Convert labels to indices but keep as list (not tensor yet)
            self.label_indices = [self.label_dict[label] for label in self.labels]

            # Print label statistics
            unique_indices, counts = np.unique(self.label_indices, return_counts=True)
            print('{split} label stat: '.format(split=split), (unique_indices, counts))

        def __len__(self):
            return len(self.image_paths)

        def __getitem__(self, idx):
            # Load and preprocess image on-demand
            image_path = self.image_paths[idx]
            try:
                image = self.preprocess(Image.open(image_path).convert('RGB'))
            except Exception as e:
                print(f"Error loading image {image_path}: {e}")
                # Return a placeholder if image loading fails
                image = torch.zeros(3, 224, 224)  # Adjust dimensions based on your preprocess output

            # Convert label to tensor
            label = torch.tensor([self.label_indices[idx]])

            return image, label

    class CachedCytoDataset(CytoDataset):
        def __init__(self, root, task_organ, task_type, preprocess, split='train', num_workers=None, disable_progress_bar=False):
            super().__init__(root, task_organ, task_type, preprocess, split)
            self.cached_images = {}
            self.disable_progress_bar = disable_progress_bar
            self.preload_images(num_workers)

        def load_single_image(self, image_path):
            """Load a single image and return it with its path as key"""
            try:
                image = self.preprocess(Image.open(image_path).convert('RGB'))
                return image_path, image
            except Exception as e:
                print(f"Error loading image {image_path}: {e}")
                # Return a placeholder if image loading fails
                return image_path, torch.zeros(3, 224, 224)

        def get_memory_usage(self):
            """Get current memory usage of the process in MB"""
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)  # Convert to MB
            return memory_mb

        def preload_images(self, num_workers=None):
            """Preload all images into memory using parallel processing"""
            if num_workers is None:
                # Use half of available CPU cores by default
                num_workers = max(1, multiprocessing.cpu_count() // 2)

            # Report initial memory usage
            initial_memory = self.get_memory_usage()
            print(f"Initial memory usage: {initial_memory:.2f} MB")
            print(f"Preloading {len(self.image_paths)} images using {num_workers} workers...")
            start_time = time.time()

            # Use ThreadPoolExecutor for parallel loading
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                # Submit all image loading tasks
                future_to_path = {
                    executor.submit(self.load_single_image, path): path
                    for path in self.image_paths
                }

                # Process results as they complete
                completed = 0
                memory_reports = []

                # Create progress bar if not disabled
                if not self.disable_progress_bar:
                    pbar = tqdm(
                        total=len(self.image_paths),
                        desc="Loading images",
                        unit="img",
                        bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]"
                    )

                for future in as_completed(future_to_path):
                    path, image = future.result()
                    self.cached_images[path] = image
                    completed += 1

                    # Update progress bar if not disabled
                    if not self.disable_progress_bar:
                        pbar.update(1)

                        # Calculate and display ETA based on current rate
                        if completed > 10:  # Wait for a few samples to get a better estimate
                            elapsed = time.time() - start_time
                            rate = completed / elapsed
                            eta = (len(self.image_paths) - completed) / rate if rate > 0 else 0

                            # Format ETA as HH:MM:SS
                            eta_str = time.strftime("%H:%M:%S", time.gmtime(eta))

                            # Report memory usage every 100 images or when hitting certain percentages
                            if completed % 100 == 0 or completed % (len(self.image_paths) // 10) == 0:
                                current_memory = self.get_memory_usage()
                                memory_diff = current_memory - initial_memory
                                memory_report = f"Mem: {current_memory:.1f}MB (+{memory_diff:.1f}MB)"
                                memory_reports.append(memory_report)

                                # Keep only the last 3 memory reports to avoid cluttering
                                if len(memory_reports) > 3:
                                    memory_reports.pop(0)

                                # Update progress bar postfix with memory info and ETA
                                pbar.set_postfix_str(f"ETA: {eta_str}, {' | '.join(memory_reports)}")
                    elif completed % 100 == 0:  # Still report progress without progress bar
                        current_memory = self.get_memory_usage()
                        elapsed = time.time() - start_time
                        rate = completed / elapsed if elapsed > 0 else 0
                        eta = (len(self.image_paths) - completed) / rate if rate > 0 else 0
                        eta_str = time.strftime("%H:%M:%S", time.gmtime(eta))
                        print(f"Loaded {completed}/{len(self.image_paths)} images. ETA: {eta_str}. Memory: {current_memory:.1f}MB (+{current_memory - initial_memory:.1f}MB)")

            # Close progress bar if it was created
            if not self.disable_progress_bar and 'pbar' in locals():
                pbar.close()

            # Force garbage collection to get accurate memory usage
            gc.collect()

            # Report final memory usage
            final_memory = self.get_memory_usage()
            end_time = time.time()
            total_time = end_time - start_time

            # Print summary
            print(f"\nFinished preloading {len(self.cached_images)} images in {total_time:.2f} seconds")
            print(f"Average loading speed: {len(self.cached_images) / total_time:.2f} images/second")
            print(f"Final memory usage: {final_memory:.2f} MB (+ {final_memory - initial_memory:.2f} MB)")

        def __getitem__(self, idx):
            # Get image from cache instead of loading from disk
            image_path = self.image_paths[idx]
            image = self.cached_images.get(image_path)

            # If image is not in cache (shouldn't happen, but just in case)
            if image is None:
                print(f"Warning: Image {image_path} not found in cache, loading from disk")
                try:
                    image = self.preprocess(Image.open(image_path).convert('RGB'))
                except Exception as e:
                    print(f"Error loading image {image_path}: {e}")
                    image = torch.zeros(3, 224, 224)

            # Convert label to tensor
            label = torch.tensor([self.label_indices[idx]])

            return image, label

    # Dataset configuration mapping
    dataset_config = {
        'Herlev': {'path': 'Herlev', 'organ': 'cervix', 'type': 'cls'},
        'HiCervix': {'path': 'HiCervix', 'organ': 'cervix', 'type': 'cls'},
        'JinWooChoi': {'path': 'JinWooChoi', 'organ': 'bone_marrow', 'type': 'cls'},
        'FNAC2019': {'path': 'FNAC2019', 'organ': 'breast', 'type': 'cls'},
        'LDCC': {'path': 'LDCC', 'organ': 'cervix', 'type': 'cls'},
        'Sipakmed': {'path': 'Sipakmed', 'organ': 'cervix', 'type': 'cls'},
        'Barcelona': {'path': 'Barcelona', 'organ': 'bone_marrow', 'type': 'cls'},
        'BCI': {'path': 'Blood_Cell_Images', 'organ': 'bone_marrow', 'type': 'cls'},
        'BCFCI': {'path': 'Body_cavity_fluid_cytology_images', 'organ': 'body_fluid', 'type': 'cls'},
        'BMCC': {'path': 'Bone_Marrow_Cell_Classification', 'organ': 'bone_marrow', 'type': 'cls'}
    }

    if dataset_name not in available_datasets:
        raise NotImplementedError(f"Dataset {dataset_name} not available. Choose from: {available_datasets}")

    config = dataset_config[dataset_name]
    dataset_path = os.path.join(dataset_root, config['path'])

    # Create datasets for each split
    if use_cache:
        print(f"Using cached dataset for {dataset_name}")
        dataset = {
            'train': CachedCytoDataset(dataset_path, config['organ'], config['type'], preprocess,
                                      split='train', num_workers=num_workers, disable_progress_bar=disable_progress_bar),
            'val': CachedCytoDataset(dataset_path, config['organ'], config['type'], preprocess,
                                    split='val', num_workers=num_workers, disable_progress_bar=disable_progress_bar),
            'test': CachedCytoDataset(dataset_path, config['organ'], config['type'], preprocess,
                                     split='test', num_workers=num_workers, disable_progress_bar=disable_progress_bar)
        }
    else:
        print(f"Using standard dataset for {dataset_name} (loading images on-demand)")
        dataset = {
            'train': CytoDataset(dataset_path, config['organ'], config['type'], preprocess, split='train'),
            'val': CytoDataset(dataset_path, config['organ'], config['type'], preprocess, split='val'),
            'test': CytoDataset(dataset_path, config['organ'], config['type'], preprocess, split='test')
        }

    return dataset


def build_kfold_dataset(dataset_name, preprocess, k=5, use_cache=False, num_workers=None, disable_progress_bar=False):
    """
    Build k-fold cross-validation datasets by combining all available data and splitting into k folds

    Args:
        dataset_name: Name of the dataset
        preprocess: Preprocessing function
        k: Number of folds
        use_cache: Whether to cache images in memory
        num_workers: Number of workers for data loading
        disable_progress_bar: Whether to disable progress bars

    Returns:
        folds: List of k dictionaries, each containing 'train' and 'test' datasets for that fold
    """
    from sklearn.model_selection import StratifiedKFold

    available_datasets = ['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC']
    dataset_root = '/jhcnas4/jh/cytology/CYTO_task'

    # Dataset configuration mapping
    dataset_config = {
        'Herlev': {'path': 'Herlev', 'organ': 'cervix', 'type': 'cls'},
        'HiCervix': {'path': 'HiCervix', 'organ': 'cervix', 'type': 'cls'},
        'JinWooChoi': {'path': 'JinWooChoi', 'organ': 'bone_marrow', 'type': 'cls'},
        'FNAC2019': {'path': 'FNAC2019', 'organ': 'breast', 'type': 'cls'},
        'LDCC': {'path': 'LDCC', 'organ': 'cervix', 'type': 'cls'},
        'Sipakmed': {'path': 'Sipakmed', 'organ': 'cervix', 'type': 'cls'},
        'Barcelona': {'path': 'Barcelona', 'organ': 'bone_marrow', 'type': 'cls'},
        'BCI': {'path': 'Blood_Cell_Images', 'organ': 'bone_marrow', 'type': 'cls'},
        'BCFCI': {'path': 'Body_cavity_fluid_cytology_images', 'organ': 'body_fluid', 'type': 'cls'},
        'BMCC': {'path': 'Bone_Marrow_Cell_Classification', 'organ': 'bone_marrow', 'type': 'cls'}
    }

    if dataset_name not in available_datasets:
        raise NotImplementedError(f"Dataset {dataset_name} not available. Choose from: {available_datasets}")

    config = dataset_config[dataset_name]
    dataset_path = os.path.join(dataset_root, config['path'])

    # Collect all data from train, val, and test splits
    all_image_paths = []
    all_labels = []

    # Load data from all available splits
    for split in ['train', 'val', 'test']:
        split_file = os.path.join(dataset_path, f'{split}.txt')
        if os.path.exists(split_file):
            with open(split_file, 'r') as f:
                lines = f.readlines()

            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    all_image_paths.append(os.path.join(dataset_path, parts[0]))
                    all_labels.append(parts[-1])
        else:
            print(f"Warning: {split_file} not found, skipping {split} split")

    if not all_image_paths:
        raise ValueError(f"No data found for dataset {dataset_name}")

    # Create label mapping
    unique_labels = sorted(set(all_labels))
    label_dict = {label: i for i, label in enumerate(unique_labels)}
    label_indices = [label_dict[label] for label in all_labels]

    print(f"Total samples for k-fold: {len(all_image_paths)}")
    print(f"Label distribution: {dict(zip(*np.unique(label_indices, return_counts=True)))}")

    # Create stratified k-fold splits
    skf = StratifiedKFold(n_splits=k, shuffle=True, random_state=42)
    folds = []

    for fold_idx, (train_indices, test_indices) in enumerate(skf.split(all_image_paths, label_indices)):
        print(f"Creating fold {fold_idx + 1}/{k}")

        # Create train and test datasets for this fold
        train_paths = [all_image_paths[i] for i in train_indices]
        train_labels = [all_labels[i] for i in train_indices]
        test_paths = [all_image_paths[i] for i in test_indices]
        test_labels = [all_labels[i] for i in test_indices]

        # Create custom dataset classes for this fold
        class KFoldDataset(torch.utils.data.Dataset):
            def __init__(self, image_paths, labels, label_dict, preprocess, task_type):
                self.image_paths = image_paths
                self.labels = labels
                self.label_dict = label_dict
                self.preprocess = preprocess
                self.task_type = task_type
                self.label_indices = [label_dict[label] for label in labels]

            def __len__(self):
                return len(self.image_paths)

            def __getitem__(self, idx):
                image_path = self.image_paths[idx]
                label = self.label_indices[idx]

                try:
                    image = self.preprocess(Image.open(image_path).convert('RGB'))
                except Exception as e:
                    print(f"Error loading image {image_path}: {e}")
                    # Return a placeholder if image loading fails
                    image = torch.zeros(3, 224, 224)

                return image, label

        # Create datasets for this fold
        if use_cache:
            # For k-fold, we'll create a cached version of KFoldDataset
            class KFoldCachedDataset(KFoldDataset):
                def __init__(self, image_paths, labels, label_dict, preprocess, task_type, num_workers=None, disable_progress_bar=False):
                    super().__init__(image_paths, labels, label_dict, preprocess, task_type)
                    self.cached_images = {}
                    self.disable_progress_bar = disable_progress_bar
                    self.preload_images(num_workers)

                def preload_images(self, num_workers=None):
                    """Preload all images into memory"""
                    if num_workers is None:
                        num_workers = max(1, multiprocessing.cpu_count() // 2)

                    print(f"Preloading {len(self.image_paths)} images for k-fold...")

                    # Simple sequential loading for k-fold (can be optimized later)
                    for i, image_path in enumerate(self.image_paths):
                        try:
                            image = self.preprocess(Image.open(image_path).convert('RGB'))
                            self.cached_images[image_path] = image
                        except Exception as e:
                            print(f"Error loading image {image_path}: {e}")
                            self.cached_images[image_path] = torch.zeros(3, 224, 224)

                        if not self.disable_progress_bar and (i + 1) % 100 == 0:
                            print(f"Loaded {i + 1}/{len(self.image_paths)} images")

                    print(f"Finished preloading {len(self.cached_images)} images")

                def __getitem__(self, idx):
                    image_path = self.image_paths[idx]
                    image = self.cached_images.get(image_path)

                    if image is None:
                        # Fallback to loading from disk
                        try:
                            image = self.preprocess(Image.open(image_path).convert('RGB'))
                        except Exception as e:
                            print(f"Error loading image {image_path}: {e}")
                            image = torch.zeros(3, 224, 224)

                    label = self.label_indices[idx]
                    return image, label

            # Create cached datasets
            train_dataset = KFoldCachedDataset(train_paths, train_labels, label_dict, preprocess, config['type'],
                                             num_workers=num_workers, disable_progress_bar=disable_progress_bar)
            test_dataset = KFoldCachedDataset(test_paths, test_labels, label_dict, preprocess, config['type'],
                                            num_workers=num_workers, disable_progress_bar=disable_progress_bar)
        else:
            # Create simple datasets
            train_dataset = KFoldDataset(train_paths, train_labels, label_dict, preprocess, config['type'])
            test_dataset = KFoldDataset(test_paths, test_labels, label_dict, preprocess, config['type'])

        # Store fold data
        fold_data = {
            'train': train_dataset,
            'test': test_dataset
        }
        folds.append(fold_data)

        print(f"Fold {fold_idx + 1}: Train={len(train_dataset)}, Test={len(test_dataset)}")

    return folds


def train_epoch(model, train_loader, criterion, optimizer, device):
    """
    Train the model for one epoch

    Args:
        model: The model to train
        train_loader: DataLoader for training data
        criterion: Loss function
        optimizer: Optimizer
        device: Device to use for training

    Returns:
        train_loss: Average training loss
        train_acc: Training accuracy
    """
    model.train()  # Set model to training mode, but only classifier will compute gradients
    train_loss = 0.0
    train_correct = 0
    train_total = 0

    for i, (images, labels) in enumerate(train_loader):
        try:
            # Move tensors to the specified device
            images = images.to(device)
            # Reshape labels from [batch_size, 1] to [batch_size]
            labels = labels.squeeze().to(device)

            # Forward pass
            outputs = model(images)
            if labels.shape == torch.Size([]):
                labels = labels.unsqueeze(0)
            loss = criterion(outputs, labels)

            # Backward and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # Calculate accuracy
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            train_loss += loss.item()

            if (i+1) % 10 == 0:
                print('Step [{}/{}], Loss: {:.4f}, Acc: {:.2f}%'
                    .format(i+1, len(train_loader), loss.item(),
                            100 * train_correct / train_total))

        except RuntimeError as e:
            if 'out of memory' in str(e):
                print('| WARNING: ran out of memory, skipping batch')
                if hasattr(torch.cuda, 'empty_cache'):
                    torch.cuda.empty_cache()
            else:
                raise e

    # Calculate average loss and accuracy
    avg_train_loss = train_loss / len(train_loader)
    train_acc = 100 * train_correct / train_total

    return avg_train_loss, train_acc


def validate(model, val_loader, criterion, device):
    """
    Validate the model

    Args:
        model: The model to validate
        val_loader: DataLoader for validation data
        criterion: Loss function
        device: Device to use for validation

    Returns:
        val_loss: Average validation loss
        val_acc: Validation accuracy
    """
    model.eval()
    val_loss = 0.0
    val_correct = 0
    val_total = 0

    with torch.no_grad():
        for images, labels in val_loader:
            try:
                images = images.to(device)
                labels = labels.squeeze().to(device)

                outputs = model(images)
                loss = criterion(outputs, labels)

                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
                val_loss += loss.item()
            except RuntimeError as e:
                if 'out of memory' in str(e):
                    print('| WARNING: ran out of memory during validation, skipping batch')
                    if hasattr(torch.cuda, 'empty_cache'):
                        torch.cuda.empty_cache()
                else:
                    raise e

    # Calculate average loss and accuracy
    if val_total > 0:  # Avoid division by zero if all batches were skipped
        avg_val_loss = val_loss / len(val_loader)
        val_acc = 100 * val_correct / val_total
    else:
        avg_val_loss = float('inf')
        val_acc = 0.0
        print("WARNING: All validation batches were skipped due to memory issues")

    return avg_val_loss, val_acc


def test(model, test_loader, criterion, device, label_dict):
    """
    Test the model and compute metrics

    Args:
        model: The model to test
        test_loader: DataLoader for test data
        criterion: Loss function
        device: Device to use for testing
        label_dict: Dictionary mapping label names to indices

    Returns:
        test_loss: Average test loss
        test_acc: Test accuracy
        confusion_matrix: Confusion matrix
        per_class_acc: Per-class accuracy
    """
    model.eval()
    test_loss = 0.0
    test_correct = 0
    test_total = 0

    # Create confusion matrix
    num_classes = len(label_dict)
    confusion_matrix = torch.zeros(num_classes, num_classes)

    with torch.no_grad():
        for images, labels in test_loader:
            try:
                images = images.to(device)
                labels = labels.squeeze().to(device)

                outputs = model(images)
                loss = criterion(outputs, labels)

                _, predicted = torch.max(outputs.data, 1)
                test_total += labels.size(0)
                test_correct += (predicted == labels).sum().item()
                test_loss += loss.item()

                # Update confusion matrix
                for t, p in zip(labels.view(-1), predicted.view(-1)):
                    confusion_matrix[t.long(), p.long()] += 1
            except RuntimeError as e:
                if 'out of memory' in str(e):
                    print('| WARNING: ran out of memory during testing, skipping batch')
                    if hasattr(torch.cuda, 'empty_cache'):
                        torch.cuda.empty_cache()
                else:
                    raise e

    # Calculate average loss and accuracy
    if test_total > 0:  # Avoid division by zero if all batches were skipped
        avg_test_loss = test_loss / len(test_loader)
        test_acc = 100 * test_correct / test_total

        # Calculate per-class accuracy
        per_class_acc = confusion_matrix.diag() / confusion_matrix.sum(1).clamp(min=1e-10)  # Avoid division by zero
    else:
        avg_test_loss = float('inf')
        test_acc = 0.0
        per_class_acc = torch.zeros(num_classes)
        print("WARNING: All test batches were skipped due to memory issues")

    return avg_test_loss, test_acc, confusion_matrix, per_class_acc


def save_checkpoint(model, optimizer, epoch, val_acc, model_name, dataset_name):
    """
    Save model checkpoint

    Args:
        model: The model to save
        optimizer: The optimizer
        epoch: Current epoch
        val_acc: Validation accuracy
        model_name: Name of the model
        dataset_name: Name of the dataset

    Returns:
        checkpoint_path: Path to the saved checkpoint
    """
    # Create directory if it doesn't exist
    os.makedirs('checkpoints', exist_ok=True)

    # Create checkpoint path
    checkpoint_path = f'checkpoints/{model_name}_{dataset_name}_best.pth'

    # Save model
    torch.save({
        'epoch': epoch + 1,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_acc': val_acc,
    }, checkpoint_path)

    return checkpoint_path


def train_model(model, train_loader, val_loader, criterion, optimizer, device, num_epochs, model_name, dataset_name, task_type='cls'):
    """
    Train the model for multiple epochs

    Args:
        model: The model to train
        train_loader: DataLoader for training data
        val_loader: DataLoader for validation data
        criterion: Loss function
        optimizer: Optimizer
        device: Device to use for training
        num_epochs: Number of epochs to train for
        model_name: Name of the model
        dataset_name: Name of the dataset
        task_type: Task type ('cls', 'seg', or 'det')

    Returns:
        model: The trained model
        best_epoch: The epoch with the best validation accuracy
        best_val_acc: The best validation accuracy
    """
    # Initialize variables for tracking best model
    best_val_acc = 0.0
    best_epoch = 0

    # Print task type
    print(f"Training model for task type: {task_type}")

    # Training loop
    for e in range(num_epochs):
        print(f"\nEpoch {e+1}/{num_epochs}")
        print("-" * 20)

        # Training phase
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)

        # Validation phase
        val_loss, val_acc = validate(model, val_loader, criterion, device)

        print(f'Epoch [{e+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '
              f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')

        # Save the best model based on validation accuracy
        if e == 0 or val_acc > best_val_acc:
            best_val_acc = val_acc
            best_epoch = e + 1
            save_checkpoint(model, optimizer, e, val_acc, model_name, dataset_name)
            print(f'Best model saved at epoch {best_epoch} with validation accuracy {best_val_acc:.2f}%')

    # Load the best model
    print(f"\nLoading best model from epoch {best_epoch}...")
    checkpoint = torch.load(f'checkpoints/{model_name}_{dataset_name}_best.pth')
    model.load_state_dict(checkpoint['model_state_dict'])

    return model, best_epoch, best_val_acc


def build_linear_probe_model(base_model, in_features, num_classes):
    """
    Create a linear probe model by adding a linear layer on top of a frozen base model

    Args:
        base_model: The base model to use for feature extraction (can be a model or a function)
        in_features: Number of input features for the linear layer
        num_classes: Number of output classes

    Returns:
        model: The linear probe model
    """
    # Create a new model that combines the base model with a trainable classifier
    class LinearProbeModel(torch.nn.Module):
        def __init__(self, base_model, in_features, num_classes):
            super(LinearProbeModel, self).__init__()
            self.base_model = base_model
            self.classifier = torch.nn.Linear(in_features, num_classes)

        def forward(self, x):
            # Get features from the base model
            features = self.base_model(x)
            return self.classifier(features)

    # Create the combined model
    model = LinearProbeModel(base_model, in_features, num_classes)

    return model


def detect_feature_dimension(model, args):
    """
    Detect the feature dimension of the model

    Args:
        model: The model or function to detect the feature dimension for
        args: Command line arguments

    Returns:
        in_features: The detected feature dimension
    """
    try:
        # Try to determine the feature dimension by running a forward pass
        with torch.no_grad():
            # Determine if model is a function or a model object
            is_function = callable(model) and not isinstance(model, torch.nn.Module)

            # Get device - different approach based on whether it's a function or model
            if is_function:
                # For functions, we need to use the device from args
                device = torch.device(f"cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
            else:
                # For model objects, we can get the device from parameters
                device = next(model.parameters()).device

            # Create a small dummy input on the correct device
            # Try different image sizes as different models may expect different input sizes
            try:
                # First try with 224x224 (common size for many models)
                dummy_input = torch.randn(1, 3, 224, 224, device=device)
                print(f"Created dummy input (224x224) on device: {device}")
                features = model(dummy_input)
            except Exception as size_error:
                if "height" in str(size_error) and "256" in str(size_error):
                    # If error mentions height 256, try with 256x256
                    print(f"Retrying with 256x256 input size due to: {size_error}")
                    dummy_input = torch.randn(1, 3, 256, 256, device=device)
                    print(f"Created dummy input (256x256) on device: {device}")
                    features = model(dummy_input)
                else:
                    # Re-raise the error if it's not about input size
                    raise

            # Get the feature dimension
            in_features = features.shape[1]
            print(f"Detected feature dimension: {in_features}")
    except Exception as e:
        print(f"Error detecting feature dimension automatically: {e}")
        # Get the model name from the function arguments or from args
        if hasattr(args, 'model'):
            model_name = args.model.lower()
        else:
            # Extract model name from the model object or function name
            if callable(model) and hasattr(model, '__name__'):
                model_name = model.__name__.lower()
            else:
                # Try to get the model name from the model's class
                try:
                    model_name = model.__class__.__name__.lower()
                except:
                    # If all else fails, use a default
                    model_name = "unknown"

        # Determine feature dimension based on model name
        if 'clip' in model_name and 'siglip' not in model_name:
            in_features = 768   # CLIP ViT-L-14 has 768 features
        elif 'siglip' in model_name and 'siglip2' not in model_name:
            in_features = 1024  # SigLIP ViT-L-16 has 1024 features
        elif 'siglip2-vit-b' in model_name or 'vit-b' in model_name:
            in_features = 768   # SigLIP2 ViT-B has 768 features
        elif 'siglip2-vit-l' in model_name or 'vit-l' in model_name:
            in_features = 1024  # SigLIP2 ViT-L has 1024 features
        elif 'siglip2-vit-so400m' in model_name or 'vit-so400m' in model_name:
            in_features = 1152  # SigLIP2 ViT-SO400M has 1536 features
        else:
            in_features = 768   # Default fallback
        print(f"Using default feature dimension for model: {in_features}")

    return in_features


# Import necessary libraries for metrics
try:
    from sklearn.metrics import precision_recall_fscore_support
    from sklearn.metrics import accuracy_score, confusion_matrix as sk_confusion_matrix
    from sklearn.metrics import roc_auc_score, roc_curve, auc
    from sklearn.preprocessing import label_binarize
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available, some metrics will not be computed")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: numpy not available, some metrics will not be computed")


def bootstrap_ci(data, metric_func, n_bootstraps=1000, ci=95):
    """
    Compute bootstrap confidence intervals for a metric

    Args:
        data: Tuple of (predictions, targets)
        metric_func: Function to compute the metric
        n_bootstraps: Number of bootstrap samples
        ci: Confidence interval percentage

    Returns:
        lower_bound: Lower bound of the confidence interval
        upper_bound: Upper bound of the confidence interval
    """
    if not NUMPY_AVAILABLE:
        return None, None

    predictions, targets = data
    n_samples = len(predictions)

    # Compute bootstrap samples
    bootstrap_metrics = []

    for _ in range(n_bootstraps):
        # Sample with replacement
        indices = np.random.choice(n_samples, n_samples, replace=True)
        bootstrap_preds = predictions[indices]
        bootstrap_targets = targets[indices]

        # Compute metric on bootstrap sample
        bootstrap_metric = metric_func(bootstrap_targets, bootstrap_preds)
        bootstrap_metrics.append(bootstrap_metric)

    # Compute confidence interval
    alpha = (100 - ci) / 2 / 100
    lower_bound = np.percentile(bootstrap_metrics, alpha * 100)
    upper_bound = np.percentile(bootstrap_metrics, (1 - alpha) * 100)

    return lower_bound, upper_bound


def compute_classification_metrics(predictions, targets, num_classes, class_names=None, probabilities=None, compute_ci=True, n_bootstraps=1000):
    """
    Compute classification metrics with bootstrap confidence intervals

    Args:
        predictions: Model predictions (class indices)
        targets: Ground truth labels
        num_classes: Number of classes
        class_names: List of class names (optional)
        probabilities: Model probability outputs (softmax outputs) for AUC calculation
        compute_ci: Whether to compute confidence intervals
        n_bootstraps: Number of bootstrap samples

    Returns:
        metrics: Dictionary of metrics
    """
    if not SKLEARN_AVAILABLE or not NUMPY_AVAILABLE:
        print("Warning: scikit-learn or numpy not available, using basic metrics only")
        compute_ci = False

    # Convert tensors to numpy arrays if needed
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()

    # Ensure predictions and targets are 1D arrays
    predictions = predictions.reshape(-1)
    targets = targets.reshape(-1)

    # Initialize metrics dictionary
    metrics = {}

    # Compute accuracy
    accuracy = accuracy_score(targets, predictions) * 100
    metrics['accuracy'] = accuracy

    # Compute confidence interval for accuracy if requested
    if compute_ci:
        acc_func = lambda y_true, y_pred: accuracy_score(y_true, y_pred) * 100
        acc_lower, acc_upper = bootstrap_ci((predictions, targets), acc_func, n_bootstraps)
        metrics['accuracy_ci'] = (acc_lower, acc_upper)

    # Compute confusion matrix
    cm = sk_confusion_matrix(targets, predictions, labels=range(num_classes))
    metrics['confusion_matrix'] = cm

    # Compute per-class accuracy
    per_class_acc = cm.diagonal() / np.maximum(cm.sum(axis=1), 1)  # Avoid division by zero
    metrics['per_class_accuracy'] = per_class_acc * 100

    # Compute per-class confidence intervals if requested
    if compute_ci:
        per_class_ci = []
        for class_idx in range(num_classes):
            # Filter data for this class
            class_mask = (targets == class_idx)
            if np.sum(class_mask) > 0:
                class_targets = targets[class_mask]
                class_preds = predictions[class_mask]

                # Define per-class accuracy function
                def class_acc_func(_, y_pred):
                    # Use underscore for unused parameter
                    return np.mean(y_pred == class_idx) * 100

                # Compute CI
                lower, upper = bootstrap_ci((class_preds, class_targets), class_acc_func, n_bootstraps)
                per_class_ci.append((lower, upper))
            else:
                per_class_ci.append((0, 0))

        metrics['per_class_accuracy_ci'] = per_class_ci

    # Compute precision, recall, F1 score
    precision, recall, f1, support = precision_recall_fscore_support(
        targets, predictions, labels=range(num_classes), average=None, zero_division=0
    )
    metrics['precision'] = precision * 100
    metrics['recall'] = recall * 100
    metrics['f1_score'] = f1 * 100
    metrics['support'] = support

    # Compute confidence intervals for precision, recall, F1 if requested
    if compute_ci:
        # Precision CI
        precision_ci = []
        for class_idx in range(num_classes):
            def precision_func(y_true, y_pred):
                p, _, _, _ = precision_recall_fscore_support(
                    y_true, y_pred, labels=[class_idx], average=None, zero_division=0
                )
                return p[0] * 100

            lower, upper = bootstrap_ci((predictions, targets), precision_func, n_bootstraps)
            precision_ci.append((lower, upper))

        metrics['precision_ci'] = precision_ci

        # Recall CI
        recall_ci = []
        for class_idx in range(num_classes):
            def recall_func(y_true, y_pred):
                _, r, _, _ = precision_recall_fscore_support(
                    y_true, y_pred, labels=[class_idx], average=None, zero_division=0
                )
                return r[0] * 100

            lower, upper = bootstrap_ci((predictions, targets), recall_func, n_bootstraps)
            recall_ci.append((lower, upper))

        metrics['recall_ci'] = recall_ci

        # F1 CI
        f1_ci = []
        for class_idx in range(num_classes):
            def f1_func(y_true, y_pred):
                _, _, f, _ = precision_recall_fscore_support(
                    y_true, y_pred, labels=[class_idx], average=None, zero_division=0
                )
                return f[0] * 100

            lower, upper = bootstrap_ci((predictions, targets), f1_func, n_bootstraps)
            f1_ci.append((lower, upper))

        metrics['f1_score_ci'] = f1_ci

    # Compute macro and weighted averages
    metrics['macro_precision'] = np.mean(precision) * 100
    metrics['macro_recall'] = np.mean(recall) * 100
    metrics['macro_f1'] = np.mean(f1) * 100
    metrics['weighted_precision'] = np.average(precision, weights=support) * 100
    metrics['weighted_recall'] = np.average(recall, weights=support) * 100
    metrics['weighted_f1'] = np.average(f1, weights=support) * 100

    # Compute AUC (Area Under the ROC Curve)
    # Check if probabilities are available for AUC calculation
    if probabilities is not None:
        # Convert tensor to numpy if needed
        if isinstance(probabilities, torch.Tensor):
            probabilities = probabilities.cpu().numpy()

        # For binary classification
        if num_classes == 2:
            try:
                # Binary classification - use roc_auc_score with probability of positive class
                # For binary classification, we use the probability of class 1
                pos_class_probs = probabilities[:, 1]
                metrics['auc'] = roc_auc_score(targets, pos_class_probs) * 100

                # Compute confidence interval for AUC if requested
                if compute_ci:
                    # Create bootstrap function that uses probabilities
                    def auc_func(y_true, prob_samples):
                        try:
                            return roc_auc_score(y_true, prob_samples, multi_class='ovr') * 100
                        except:
                            return 50.0  # Default to random chance

                    # Use probabilities for bootstrap
                    auc_lower, auc_upper = bootstrap_ci((pos_class_probs, targets), auc_func, n_bootstraps)
                    metrics['auc_ci'] = (auc_lower, auc_upper)
            except Exception as e:
                print(f"Warning: Could not compute AUC for binary classification: {e}")
                metrics['auc'] = 0.0
                if compute_ci:
                    metrics['auc_ci'] = (0.0, 0.0)
        else:
            try:
                # Multi-class classification - use One-vs-Rest approach with probabilities
                # For storing AUC for each class
                auc_per_class = []
                auc_ci_per_class = []

                # Compute AUC for each class
                for i in range(num_classes):
                    # Create binary labels for this class (1 for this class, 0 for others)
                    binary_targets = (targets == i).astype(int)

                    # Use probability scores for this class
                    class_probs = probabilities[:, i]

                    # Compute AUC for this class
                    try:
                        class_auc = roc_auc_score(binary_targets, class_probs) * 100
                        auc_per_class.append(class_auc)

                        # Compute confidence interval for this class's AUC if requested
                        if compute_ci:
                            # Create bootstrap function that uses probabilities
                            def class_auc_func(y_true, prob_samples):
                                try:
                                    # Convert to binary for this class
                                    y_true_bin = (y_true == i).astype(int)
                                    return roc_auc_score(y_true_bin, prob_samples) * 100
                                except:
                                    return 50.0  # Default to random chance

                            # Use class probabilities for bootstrap
                            lower, upper = bootstrap_ci((class_probs, targets), class_auc_func, n_bootstraps)
                            auc_ci_per_class.append((lower, upper))
                    except Exception as e:
                        print(f"Warning: Could not compute AUC for class {i}: {e}")
                        auc_per_class.append(0.0)
                        if compute_ci:
                            auc_ci_per_class.append((0.0, 0.0))

                # Store per-class AUC
                metrics['auc_per_class'] = np.array(auc_per_class)
                if compute_ci:
                    metrics['auc_per_class_ci'] = auc_ci_per_class

                # Compute macro-average AUC (average of per-class AUCs)
                metrics['macro_auc'] = np.mean(auc_per_class)

                # Use macro-average AUC as the main AUC metric for multi-class
                metrics['auc'] = metrics['macro_auc']

                # Compute confidence interval for macro AUC if requested
                if compute_ci:
                    # For macro AUC CI, we'll use the already computed CIs
                    if auc_ci_per_class:
                        # Calculate mean of lower and upper bounds
                        lower_bounds = [ci[0] for ci in auc_ci_per_class]
                        upper_bounds = [ci[1] for ci in auc_ci_per_class]
                        auc_lower = np.mean(lower_bounds)
                        auc_upper = np.mean(upper_bounds)
                        metrics['auc_ci'] = (auc_lower, auc_upper)
                        metrics['macro_auc_ci'] = (auc_lower, auc_upper)
            except Exception as e:
                print(f"Warning: Could not compute AUC for multi-class classification: {e}")
                metrics['auc'] = 0.0
                metrics['macro_auc'] = 0.0
                metrics['auc_per_class'] = np.zeros(num_classes)
                if compute_ci:
                    metrics['auc_ci'] = (0.0, 0.0)
                    metrics['macro_auc_ci'] = (0.0, 0.0)
                    metrics['auc_per_class_ci'] = [(0.0, 0.0)] * num_classes
    else:
        # If probabilities are not available, set AUC to 0
        print("Warning: Probabilities not available for AUC calculation")
        metrics['auc'] = 0.0
        metrics['macro_auc'] = 0.0 if num_classes > 2 else 0.0
        metrics['auc_per_class'] = np.zeros(num_classes) if num_classes > 2 else np.array([0.0, 0.0])
        if compute_ci:
            metrics['auc_ci'] = (0.0, 0.0)
            metrics['macro_auc_ci'] = (0.0, 0.0) if num_classes > 2 else (0.0, 0.0)
            metrics['auc_per_class_ci'] = [(0.0, 0.0)] * num_classes if num_classes > 2 else [(0.0, 0.0), (0.0, 0.0)]

    # Compute confidence intervals for macro and weighted averages if requested
    if compute_ci:
        # Macro precision CI
        def macro_precision_func(y_true, y_pred):
            p, _, _, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='macro', zero_division=0
            )
            return p * 100

        lower, upper = bootstrap_ci((predictions, targets), macro_precision_func, n_bootstraps)
        metrics['macro_precision_ci'] = (lower, upper)

        # Macro recall CI
        def macro_recall_func(y_true, y_pred):
            _, r, _, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='macro', zero_division=0
            )
            return r * 100

        lower, upper = bootstrap_ci((predictions, targets), macro_recall_func, n_bootstraps)
        metrics['macro_recall_ci'] = (lower, upper)

        # Macro F1 CI
        def macro_f1_func(y_true, y_pred):
            _, _, f, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='macro', zero_division=0
            )
            return f * 100

        lower, upper = bootstrap_ci((predictions, targets), macro_f1_func, n_bootstraps)
        metrics['macro_f1_ci'] = (lower, upper)

        # Weighted precision CI
        def weighted_precision_func(y_true, y_pred):
            p, _, _, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='weighted', zero_division=0
            )
            return p * 100

        lower, upper = bootstrap_ci((predictions, targets), weighted_precision_func, n_bootstraps)
        metrics['weighted_precision_ci'] = (lower, upper)

        # Weighted recall CI
        def weighted_recall_func(y_true, y_pred):
            _, r, _, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='weighted', zero_division=0
            )
            return r * 100

        lower, upper = bootstrap_ci((predictions, targets), weighted_recall_func, n_bootstraps)
        metrics['weighted_recall_ci'] = (lower, upper)

        # Weighted F1 CI
        def weighted_f1_func(y_true, y_pred):
            _, _, f, _ = precision_recall_fscore_support(
                y_true, y_pred, labels=range(num_classes), average='weighted', zero_division=0
            )
            return f * 100

        lower, upper = bootstrap_ci((predictions, targets), weighted_f1_func, n_bootstraps)
        metrics['weighted_f1_ci'] = (lower, upper)

    # Format metrics for printing
    metrics['formatted_results'] = format_classification_results(
        metrics, class_names if class_names else [f"Class {i}" for i in range(num_classes)],
        include_ci=compute_ci
    )

    return metrics


def compute_segmentation_metrics(predictions, targets, num_classes):
    """
    Compute segmentation metrics (IoU, Dice, etc.)

    Args:
        predictions: Model predictions (class indices for each pixel)
        targets: Ground truth segmentation masks
        num_classes: Number of classes

    Returns:
        metrics: Dictionary of metrics
    """
    # This is a placeholder for future implementation
    print("Segmentation metrics computation not yet implemented")

    # Initialize metrics dictionary
    metrics = {
        'mean_iou': 0.0,
        'mean_dice': 0.0,
        'pixel_accuracy': 0.0,
        'class_iou': [0.0] * num_classes,
        'class_dice': [0.0] * num_classes
    }

    # Suppress unused variable warnings
    _ = predictions, targets

    return metrics


def compute_detection_metrics(predictions, targets, iou_threshold=0.5):
    """
    Compute detection metrics (mAP, precision, recall)

    Args:
        predictions: Model predictions (bounding boxes, class indices, confidence scores)
        targets: Ground truth bounding boxes and class indices
        iou_threshold: IoU threshold for considering a detection as correct

    Returns:
        metrics: Dictionary of metrics
    """
    # This is a placeholder for future implementation
    print("Detection metrics computation not yet implemented")

    # Initialize metrics dictionary
    metrics = {
        'mAP': 0.0,
        'precision': 0.0,
        'recall': 0.0
    }

    # Suppress unused variable warnings
    _ = predictions, targets, iou_threshold

    return metrics


def format_classification_results(metrics, class_names, include_ci=True):
    """
    Format classification results for printing with confidence intervals

    Args:
        metrics: Dictionary of metrics
        class_names: List of class names
        include_ci: Whether to include confidence intervals

    Returns:
        formatted_results: String with formatted results
    """
    lines = []
    lines.append("\n" + "="*80)
    lines.append("CLASSIFICATION METRICS")
    lines.append("="*80)

    # Overall metrics
    if include_ci and 'accuracy_ci' in metrics:
        acc_lower, acc_upper = metrics['accuracy_ci']
        lines.append(f"\nOverall Accuracy: {metrics['accuracy']:.2f}% (95% CI: {acc_lower:.2f}% - {acc_upper:.2f}%)")
    else:
        lines.append(f"\nOverall Accuracy: {metrics['accuracy']:.2f}%")

    # AUC metrics
    if 'auc' in metrics:
        if include_ci and 'auc_ci' in metrics:
            auc_lower, auc_upper = metrics['auc_ci']
            lines.append(f"AUC: {metrics['auc']:.2f}% (95% CI: {auc_lower:.2f}% - {auc_upper:.2f}%)")
        else:
            lines.append(f"AUC: {metrics['auc']:.2f}%")

    # Macro metrics with CI
    if include_ci and 'macro_precision_ci' in metrics:
        lower, upper = metrics['macro_precision_ci']
        lines.append(f"Macro Precision: {metrics['macro_precision']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Macro Precision: {metrics['macro_precision']:.2f}%")

    if include_ci and 'macro_recall_ci' in metrics:
        lower, upper = metrics['macro_recall_ci']
        lines.append(f"Macro Recall: {metrics['macro_recall']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Macro Recall: {metrics['macro_recall']:.2f}%")

    if include_ci and 'macro_f1_ci' in metrics:
        lower, upper = metrics['macro_f1_ci']
        lines.append(f"Macro F1 Score: {metrics['macro_f1']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Macro F1 Score: {metrics['macro_f1']:.2f}%")

    # Weighted metrics with CI
    if include_ci and 'weighted_precision_ci' in metrics:
        lower, upper = metrics['weighted_precision_ci']
        lines.append(f"Weighted Precision: {metrics['weighted_precision']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Weighted Precision: {metrics['weighted_precision']:.2f}%")

    if include_ci and 'weighted_recall_ci' in metrics:
        lower, upper = metrics['weighted_recall_ci']
        lines.append(f"Weighted Recall: {metrics['weighted_recall']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Weighted Recall: {metrics['weighted_recall']:.2f}%")

    if include_ci and 'weighted_f1_ci' in metrics:
        lower, upper = metrics['weighted_f1_ci']
        lines.append(f"Weighted F1 Score: {metrics['weighted_f1']:.2f}% (95% CI: {lower:.2f}% - {upper:.2f}%)")
    else:
        lines.append(f"Weighted F1 Score: {metrics['weighted_f1']:.2f}%")

    # Per-class metrics
    lines.append("\nPer-class Metrics:")

    if include_ci and 'per_class_accuracy_ci' in metrics and 'precision_ci' in metrics and 'recall_ci' in metrics and 'f1_score_ci' in metrics:
        lines.append("-"*120)
        lines.append(f"{'Class':<20} {'Accuracy':<30} {'Precision':<30} {'Recall':<30} {'F1 Score':<30}")
        lines.append("-"*120)

        for i, class_name in enumerate(class_names):
            acc = metrics['per_class_accuracy'][i]
            acc_lower, acc_upper = metrics['per_class_accuracy_ci'][i]

            prec = metrics['precision'][i]
            prec_lower, prec_upper = metrics['precision_ci'][i]

            rec = metrics['recall'][i]
            rec_lower, rec_upper = metrics['recall_ci'][i]

            f1 = metrics['f1_score'][i]
            f1_lower, f1_upper = metrics['f1_score_ci'][i]

            lines.append(f"{class_name:<20} "
                        f"{acc:.2f}% (95% CI: {acc_lower:.2f}%-{acc_upper:.2f}%) "
                        f"{prec:.2f}% (95% CI: {prec_lower:.2f}%-{prec_upper:.2f}%) "
                        f"{rec:.2f}% (95% CI: {rec_lower:.2f}%-{rec_upper:.2f}%) "
                        f"{f1:.2f}% (95% CI: {f1_lower:.2f}%-{f1_upper:.2f}%)")

        # Add support in a separate table
        lines.append("\nClass Support (number of samples):")
        lines.append("-"*50)
        for i, class_name in enumerate(class_names):
            lines.append(f"{class_name:<20} {metrics['support'][i]}")
    else:
        lines.append("-"*80)
        lines.append(f"{'Class':<20} {'Accuracy':<10} {'Precision':<10} {'Recall':<10} {'F1 Score':<10} {'Support':<10}")
        lines.append("-"*80)

        for i, class_name in enumerate(class_names):
            lines.append(f"{class_name:<20} "
                        f"{metrics['per_class_accuracy'][i]:.2f}% "
                        f"{metrics['precision'][i]:.2f}% "
                        f"{metrics['recall'][i]:.2f}% "
                        f"{metrics['f1_score'][i]:.2f}% "
                        f"{metrics['support'][i]}")

    # Confusion matrix
    lines.append("\nConfusion Matrix:")
    cm = metrics['confusion_matrix']
    header = "      " + " ".join(f"{i:5d}" for i in range(len(class_names)))
    lines.append(header)
    lines.append("-"*len(header))

    for i, row in enumerate(cm):
        lines.append(f"{i:5d} " + " ".join(f"{cell:5d}" for cell in row))

    return "\n".join(lines)


def evaluate(model, data_loader, criterion, device, task_type='cls', label_dict=None, compute_ci=True, n_bootstraps=1000, **kwargs):
    """
    Evaluate the model and compute metrics with bootstrap confidence intervals

    Args:
        model: The model to evaluate
        data_loader: DataLoader for evaluation data
        criterion: Loss function
        device: Device to use for evaluation
        task_type: Task type ('cls', 'seg', or 'det')
        label_dict: Dictionary mapping label names to indices (for classification)
        compute_ci: Whether to compute bootstrap confidence intervals
        n_bootstraps: Number of bootstrap samples for confidence intervals
        **kwargs: Additional arguments for specific task types

    Returns:
        metrics: Dictionary of metrics
    """
    model.eval()
    eval_loss = 0.0

    # Lists to store predictions, probabilities, and targets
    all_predictions = []
    all_probabilities = []  # For storing softmax outputs for AUC calculation
    all_targets = []

    with torch.no_grad():
        for batch in data_loader:
            try:
                if task_type == 'cls':
                    # Classification task
                    images, labels = batch
                    images = images.to(device)
                    labels = labels.squeeze().to(device)

                    # Forward pass
                    outputs = model(images)
                    loss = criterion(outputs, labels)

                    # Apply softmax to get probabilities
                    probabilities = torch.nn.functional.softmax(outputs, dim=1)

                    # Get predictions
                    _, predictions = torch.max(outputs.data, 1)

                    # Store predictions, probabilities, and targets
                    all_predictions.append(predictions.cpu())
                    all_probabilities.append(probabilities.cpu())
                    all_targets.append(labels.cpu())

                elif task_type == 'seg':
                    # Segmentation task (placeholder)
                    images, masks = batch
                    images = images.to(device)
                    masks = masks.to(device)

                    # Forward pass
                    outputs = model(images)
                    loss = criterion(outputs, masks)

                    # Get predictions
                    predictions = torch.argmax(outputs, dim=1)

                    # Store predictions and targets
                    all_predictions.append(predictions.cpu())
                    all_targets.append(masks.cpu())

                elif task_type == 'det':
                    # Detection task (placeholder)
                    images, targets = batch
                    images = images.to(device)

                    # Forward pass
                    outputs = model(images)
                    loss = criterion(outputs, targets)

                    # Store predictions and targets (format depends on detection framework)
                    all_predictions.append(outputs)
                    all_targets.append(targets)

                else:
                    raise ValueError(f"Unsupported task type: {task_type}")

                # Accumulate loss
                eval_loss += loss.item()

            except RuntimeError as e:
                if 'out of memory' in str(e):
                    print('| WARNING: ran out of memory during evaluation, skipping batch')
                    if hasattr(torch.cuda, 'empty_cache'):
                        torch.cuda.empty_cache()
                else:
                    raise e

    # Calculate average loss
    avg_loss = eval_loss / len(data_loader)

    # Concatenate predictions, probabilities, and targets
    if all_predictions and all_targets:
        all_predictions = torch.cat(all_predictions)
        all_targets = torch.cat(all_targets)

        # Concatenate probabilities if available
        all_probabilities = torch.cat(all_probabilities) if all_probabilities else None

        # Compute metrics based on task type
        if task_type == 'cls':
            num_classes = len(label_dict) if label_dict else all_predictions.max().item() + 1
            class_names = [k for k, _ in sorted(label_dict.items(), key=lambda x: x[1])] if label_dict else None

            # Compute classification metrics with bootstrap confidence intervals if requested
            metrics = compute_classification_metrics(
                all_predictions, all_targets, num_classes, class_names,
                probabilities=all_probabilities,
                compute_ci=compute_ci, n_bootstraps=n_bootstraps
            )

        elif task_type == 'seg':
            num_classes = kwargs.get('num_classes', all_predictions.max().item() + 1)
            metrics = compute_segmentation_metrics(all_predictions, all_targets, num_classes)

        elif task_type == 'det':
            metrics = compute_detection_metrics(all_predictions, all_targets)

        else:
            metrics = {}

        # Add loss to metrics
        metrics['loss'] = avg_loss

    else:
        print("WARNING: No valid batches during evaluation")
        metrics = {'loss': float('inf')}

    return metrics


if __name__ == '__main__':
    model, preprocess = build_model('CLIP')
    dataset = build_dataset('BMCC', preprocess)

    # Example of using the evaluate function
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    test_loader = torch.utils.data.DataLoader(dataset['test'], batch_size=32, shuffle=False)
    criterion = torch.nn.CrossEntropyLoss()

    # Evaluate the model
    metrics = evaluate(model, test_loader, criterion, device, 'cls', dataset['test'].label_dict)
    print(metrics['formatted_results'])