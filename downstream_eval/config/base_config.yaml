# Base Configuration for Linear Probing Cytology Downstream Tasks
# 细胞学下游任务线性探测基础配置文件

# ========================================
# MODEL AND DATASET SELECTION PARAMETERS
# 模型和数据集选择参数
# ========================================
models:
  - ResNet50
  - ViT-L
  - CLIP
  - SigLIP
  - SigLIP2-ViT-B
  - SigLIP2-ViT-L
  - SigLIP2-ViT-SO400M

datasets:
  - Herlev
  - HiCervix
  - JinWooChoi
  - FNAC2019
  - LDCC
  - Sipakmed
  - Barcelona
  - BCI
  - BCFCI
  - BMCC

# ========================================
# TRAINING HYPERPARAMETERS
# 训练超参数
# ========================================
training:
  batch_size: 64          # Batch size for training and evaluation
  epochs: 50              # Number of training epochs
  lr: 0.001               # Learning rate for optimizer

# ========================================
# HARDWARE AND PERFORMANCE PARAMETERS
# 硬件和性能参数
# ========================================
hardware:
  gpu: 7                  # GPU ID to use for training and evaluation
  cache_data: false       # Cache all images in memory for faster training and evaluation
  num_workers: null       # Number of worker threads for data loading and caching (null = auto)
  disable_progress_bar: false  # Disable progress bar during data loading

# ========================================
# EVALUATION AND VALIDATION PARAMETERS
# 评估和验证参数
# ========================================
evaluation:
  kfold: false            # Use k-fold cross-validation instead of official train/val/test split
  k: 3                    # Number of folds for k-fold cross-validation
  compute_ci: true        # Compute bootstrap confidence intervals for metrics
  n_bootstraps: 1000      # Number of bootstrap samples for confidence intervals

# ========================================
# OUTPUT AND REPORTING PARAMETERS
# 输出和报告参数
# ========================================
output:
  metrics:                # List of metrics to use for table generation
    - accuracy
    - auc
    - macro_f1
  force_recompute: false  # Force recomputation even if result files already exist

# ========================================
# PREDEFINED CONFIGURATIONS
# 预定义配置（可以通过覆盖上述参数来使用）
# ========================================
configurations:
  quick_test:
    models: [ResNet50]
    datasets: [Herlev]
    training:
      batch_size: 32
      epochs: 5
      lr: 0.001
    hardware:
      gpu: 0
      disable_progress_bar: true
    evaluation:
      compute_ci: false

  standard_eval:
    models: [CLIP, SigLIP, SigLIP2-ViT-L]
    datasets: [Herlev, HiCervix, FNAC2019]
    training:
      batch_size: 64
      epochs: 50
      lr: 0.001
    hardware:
      gpu: 0
    evaluation:
      compute_ci: true
      n_bootstraps: 1000
    output:
      metrics: [accuracy, auc, macro_f1]

  high_performance:
    models: [CLIP, SigLIP, SigLIP2-ViT-B, SigLIP2-ViT-L, SigLIP2-ViT-SO400M]
    datasets: [Herlev, HiCervix, JinWooChoi, FNAC2019, LDCC, Sipakmed]
    training:
      batch_size: 128
      epochs: 100
      lr: 0.0005
    hardware:
      gpu: 0
      cache_data: true
      num_workers: 16
    evaluation:
      compute_ci: true
      n_bootstraps: 2000
    output:
      metrics: [accuracy, auc, macro_f1, weighted_f1]

  kfold_validation:
    models: [CLIP, SigLIP]
    datasets: [Herlev, FNAC2019]
    training:
      batch_size: 64
      epochs: 50
      lr: 0.001
    hardware:
      gpu: 0
    evaluation:
      kfold: true
      k: 5
      compute_ci: true
      n_bootstraps: 1000
    output:
      metrics: [accuracy, auc, macro_f1]

  full_evaluation:
    # Uses all default models and datasets
    training:
      batch_size: 64
      epochs: 50
      lr: 0.001
    hardware:
      gpu: 0
      cache_data: true
      num_workers: 8
    evaluation:
      compute_ci: true
      n_bootstraps: 1000
    output:
      metrics: [accuracy, auc, macro_f1, weighted_f1]

  memory_limited:
    models: [ResNet50, CLIP]
    datasets: [Herlev, FNAC2019]
    training:
      batch_size: 16
      epochs: 30
      lr: 0.001
    hardware:
      gpu: 0
      cache_data: false
      num_workers: 2
      disable_progress_bar: true
    evaluation:
      compute_ci: false
