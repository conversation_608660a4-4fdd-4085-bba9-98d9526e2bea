#!/usr/bin/env python3
"""
快速运行脚本 - 使用预定义配置快速启动评估
Quick Run Script - Launch evaluation with predefined configurations
"""

import argparse
import subprocess
import sys
from config_templates import get_config, print_all_configs

def run_with_config(config_name, dry_run=False, additional_args=None):
    """
    使用指定配置运行main.py
    Run main.py with specified configuration
    
    Args:
        config_name: 配置名称
        dry_run: 是否只显示命令而不执行
        additional_args: 额外的命令行参数
    """
    config = get_config(config_name)
    if config is None:
        print(f"错误: 未找到配置 '{config_name}'")
        print("可用配置:")
        print("- quick_test")
        print("- standard")
        print("- high_performance") 
        print("- kfold")
        print("- full")
        print("- memory_limited")
        return False
    
    # 构建命令行参数
    cmd = ['python', 'main.py']
    
    for key, value in config.items():
        if isinstance(value, bool):
            if value:  # 只有当值为True时才添加标志
                cmd.append(f'--{key}')
        elif isinstance(value, list):
            cmd.append(f'--{key}')
            cmd.extend(map(str, value))
        else:
            cmd.append(f'--{key}')
            cmd.append(str(value))
    
    # 添加额外参数
    if additional_args:
        cmd.extend(additional_args)
    
    # 显示将要执行的命令
    print(f"使用配置: {config_name.upper()}")
    print("=" * 60)
    print("将要执行的命令:")
    print(' '.join(cmd))
    print("=" * 60)
    
    if dry_run:
        print("(干运行模式 - 不会实际执行)")
        return True
    
    # 询问用户确认
    response = input("是否继续执行? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("已取消执行")
        return False
    
    # 执行命令
    try:
        print("开始执行...")
        result = subprocess.run(cmd, check=True)
        print("执行完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"执行失败，错误代码: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n用户中断执行")
        return False

def main():
    parser = argparse.ArgumentParser(description='使用预定义配置快速运行评估')
    parser.add_argument('config', nargs='?', 
                        help='配置名称 (quick_test, standard, high_performance, kfold, full, memory_limited)')
    parser.add_argument('--list', action='store_true',
                        help='列出所有可用配置')
    parser.add_argument('--dry-run', action='store_true',
                        help='只显示命令，不实际执行')
    parser.add_argument('--gpu', type=int,
                        help='覆盖配置中的GPU设置')
    parser.add_argument('--batch-size', type=int,
                        help='覆盖配置中的批次大小设置')
    parser.add_argument('--epochs', type=int,
                        help='覆盖配置中的训练轮数设置')
    
    args = parser.parse_args()
    
    if args.list:
        print_all_configs()
        return
    
    if not args.config:
        print("请指定配置名称或使用 --list 查看所有配置")
        print("\n使用示例:")
        print("  python run_config.py quick_test")
        print("  python run_config.py standard --gpu 1")
        print("  python run_config.py --list")
        return
    
    # 构建额外参数
    additional_args = []
    if args.gpu is not None:
        additional_args.extend(['--gpu', str(args.gpu)])
    if args.batch_size is not None:
        additional_args.extend(['--batch_size', str(args.batch_size)])
    if args.epochs is not None:
        additional_args.extend(['--epochs', str(args.epochs)])
    
    # 运行配置
    success = run_with_config(args.config, args.dry_run, additional_args)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
