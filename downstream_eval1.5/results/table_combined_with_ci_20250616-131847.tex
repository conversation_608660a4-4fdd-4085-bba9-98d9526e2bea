% Requires packages: multirow, graphicx
% \usepackage{multirow}
% \usepackage{graphicx}

\begin{table}[ht]
\centering
\caption{Combined evaluation results for multiple metrics}
\label{tab:combined_results}
\resizebox{	extwidth}{!}{%
\begin{tabular}{lccc}
\hline
\multirow{2}{*}{Pretrain settings} & \multicolumn{3}{c}{<PERSON><PERSON>} \\
  & Accuracy & Auc & Macro_f1 \\
\hline
ResNet50 & \textbf{0.514 (0.443 - 0.585)} & \textbf{0.894 (0.842 - 0.938)} & \textbf{0.477 (0.402 - 0.536)} \\
\hline
\end{tabular}
}
\end{table}