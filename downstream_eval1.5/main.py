# main.py for linear probing
import argparse
import os
import re
import helper
import torch
import utils
import time
import numpy as np
import yaml

def load_config_from_yaml(config_path):
    """
    Load configuration from YAML file and convert to argparse-like namespace

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        args: Namespace object with configuration parameters
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Create a namespace object to mimic argparse behavior
    args = argparse.Namespace()

    # Model configuration
    model_config = config.get('model_config', {})
    args.models = model_config.get('models', ['ResNet50', 'ViT-L', 'CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M'])
    args.gpu = model_config.get('gpu', 7)

    # Dataset configuration
    dataset_config = config.get('dataset_config', {})
    args.datasets = dataset_config.get('datasets', ['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC'])
    args.batch_size = dataset_config.get('batch_size', 64)
    args.cache_data = dataset_config.get('cache_data', False)
    args.num_workers = dataset_config.get('num_workers', None)
    args.disable_progress_bar = dataset_config.get('disable_progress_bar', False)

    # Training configuration
    training_config = config.get('training_config', {})
    args.epochs = training_config.get('epochs', 50)
    args.lr = training_config.get('lr', 1e-3)

    # Evaluation configuration
    evaluation_config = config.get('evaluation_config', {})
    args.compute_ci = evaluation_config.get('compute_ci', True)
    args.n_bootstraps = evaluation_config.get('n_bootstraps', 1000)
    args.metrics = evaluation_config.get('metrics', ['accuracy', 'auc', 'macro_f1'])
    args.force_recompute = evaluation_config.get('force_recompute', False)
    args.kfold = evaluation_config.get('kfold', False)
    args.k = evaluation_config.get('k', 3)

    return args

def evaluate_model_on_dataset(model_name, dataset_name, args):
    """
    Evaluate a single model on a single dataset

    Args:
        model_name: Name of the model to evaluate
        dataset_name: Name of the dataset to evaluate on
        args: Command line arguments

    Returns:
        metrics: Dictionary of evaluation metrics
    """
    print(f"\n{'='*80}")
    print(f"Evaluating {model_name} on {dataset_name}")
    print(f"{'='*80}")

    # Check if result file already exists
    os.makedirs('results', exist_ok=True)
    ci_suffix = "_with_ci" if args.compute_ci else ""
    result_file = f'results/{model_name}_{dataset_name}{ci_suffix}_metrics.txt'

    if os.path.exists(result_file) and not args.force_recompute:
        print(f"Result file {result_file} already exists. Skipping evaluation.")

        # Read the existing results file
        with open(result_file, 'r') as f:
            formatted_results = f.read()

        # Create a minimal metrics dictionary with the formatted results
        metrics = {
            'formatted_results': formatted_results,
            # Add placeholder values for metrics that might be needed for table generation
            'accuracy': 0.0,
            'macro_f1': 0.0,
            'weighted_f1': 0.0,
            'auc': 0.0
        }

        # Parse the formatted results to extract actual metrics
        try:
            # Extract accuracy
            acc_match = re.search(r'Overall Accuracy: (\d+\.\d+)%', formatted_results)
            if acc_match:
                metrics['accuracy'] = float(acc_match.group(1))

            # Extract AUC if available
            auc_match = re.search(r'AUC: (\d+\.\d+)%', formatted_results)
            if auc_match:
                metrics['auc'] = float(auc_match.group(1))

            # Extract F1 scores
            macro_f1_match = re.search(r'Macro F1 Score: (\d+\.\d+)%', formatted_results)
            if macro_f1_match:
                metrics['macro_f1'] = float(macro_f1_match.group(1))

            weighted_f1_match = re.search(r'Weighted F1 Score: (\d+\.\d+)%', formatted_results)
            if weighted_f1_match:
                metrics['weighted_f1'] = float(weighted_f1_match.group(1))

            # If confidence intervals were computed, extract them too
            if args.compute_ci:
                # Extract accuracy CI
                acc_ci_match = re.search(r'Overall Accuracy: \d+\.\d+% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
                if acc_ci_match:
                    metrics['accuracy_ci'] = (float(acc_ci_match.group(1)), float(acc_ci_match.group(2)))

                # Extract AUC CI if available
                auc_ci_match = re.search(r'AUC: \d+\.\d+% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
                if auc_ci_match:
                    metrics['auc_ci'] = (float(auc_ci_match.group(1)), float(auc_ci_match.group(2)))

                # Extract F1 CIs
                macro_f1_ci_match = re.search(r'Macro F1 Score: \d+\.\d+% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
                if macro_f1_ci_match:
                    metrics['macro_f1_ci'] = (float(macro_f1_ci_match.group(1)), float(macro_f1_ci_match.group(2)))

                weighted_f1_ci_match = re.search(r'Weighted F1 Score: \d+\.\d+% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
                if weighted_f1_ci_match:
                    metrics['weighted_f1_ci'] = (float(weighted_f1_ci_match.group(1)), float(weighted_f1_ci_match.group(2)))
        except Exception as e:
            print(f"Warning: Error parsing metrics from file: {e}")
            print("Using placeholder values for metrics.")

        return metrics

    # If result file doesn't exist or force_recompute is True, proceed with evaluation
    # Set device
    device = torch.device(f"cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Build model and dataset
    model, preprocess = helper.build_model(model_name)

    # Freeze all parameters in the base model
    for param in model.parameters():
        param.requires_grad = False

    # Move the base model to the correct device first
    model = model.to(device)
    print("Base model moved to device and all parameters frozen")

    # Get the image encoder function - create a proper function that extracts the image encoding part
    def get_image_encoder(model):
        # Return a function that takes an image and returns its embedding
        # if the model is ResNet50, return the output of the model directly
        if model_name == 'ResNet50' or model_name == 'ViT-L':
            return model
        return lambda x: model.encode_image(x)

    model_function = get_image_encoder(model)

    # Build dataset with caching if enabled
    print(f"Building dataset with cache_enabled={args.cache_data}, num_workers={args.num_workers}, disable_progress_bar={args.disable_progress_bar}")
    dataset = helper.build_dataset(
        dataset_name,
        preprocess,
        use_cache=args.cache_data,
        num_workers=args.num_workers,
        disable_progress_bar=args.disable_progress_bar
    )

    # Get the number of classes from the dataset
    num_classes = len(dataset['train'].label_dict)

    # Detect feature dimension
    # Create a custom args object with the model name for backward compatibility
    class ModelArgs:
        def __init__(self, model_name, gpu):
            self.model = model_name
            self.gpu = gpu

    model_args = ModelArgs(model_name, args.gpu)
    in_features = helper.detect_feature_dimension(model_function, model_args)

    # Create a wrapper function that ensures inputs are on the correct device
    def device_aware_encoder(x):
        with torch.no_grad():  # Ensure no gradients are computed in the base model
            return model_function(x)

    # Build linear probe model
    model = helper.build_linear_probe_model(device_aware_encoder, in_features, num_classes)
    model = model.to(device)

    # Verify that only classifier parameters require gradients
    trainable_params = sum(p.numel() for p in model.classifier.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Trainable parameters: {trainable_params} (should equal total: {total_params})")

    # Create data loaders
    bs = args.batch_size
    train_loader = torch.utils.data.DataLoader(dataset['train'], batch_size=bs, shuffle=True)
    print(f"Training set has {len(dataset['train'])} samples")

    val_loader = torch.utils.data.DataLoader(dataset['val'], batch_size=bs, shuffle=False)
    print(f"Validation set has {len(dataset['val'])} samples")

    test_loader = torch.utils.data.DataLoader(dataset['test'], batch_size=bs, shuffle=False)
    print(f"Test set has {len(dataset['test'])} samples")

    # Define optimizer - only train the classifier parameters
    optimizer = torch.optim.Adam(model.classifier.parameters(), lr=args.lr)

    # Get task type from dataset
    task_type = dataset['train'].task_type
    print(f"Task type: {task_type}")

    # Define loss function
    criterion = torch.nn.CrossEntropyLoss()

    # Train the model
    model, _, _ = helper.train_model(
        model, train_loader, val_loader, criterion, optimizer, device,
        args.epochs, model_name, dataset_name, task_type=task_type
    )

    # Test the model using the evaluation function
    print("\nEvaluating model on test set...")
    print(f"Computing bootstrap confidence intervals: {args.compute_ci}")
    if args.compute_ci:
        print(f"Number of bootstrap samples: {args.n_bootstraps}")

    metrics = helper.evaluate(
        model, test_loader, criterion, device,
        task_type=task_type, label_dict=dataset['train'].label_dict,
        compute_ci=args.compute_ci, n_bootstraps=args.n_bootstraps
    )

    # Print formatted results
    print(metrics['formatted_results'])

    # Save individual metrics to file
    with open(result_file, 'w') as f:
        f.write(metrics['formatted_results'])
    print(f"\nResults saved to {result_file}")

    return metrics


def evaluate_model_on_dataset_kfold(model_name, dataset_name, args):
    """
    Evaluate a single model on a single dataset using k-fold cross-validation

    Args:
        model_name: Name of the model to evaluate
        dataset_name: Name of the dataset to evaluate on
        args: Command line arguments

    Returns:
        metrics: Dictionary of evaluation metrics with mean and CI across folds
    """
    print(f"\n{'='*80}")
    print(f"Evaluating {model_name} on {dataset_name} using {args.k}-fold cross-validation")
    print(f"{'='*80}")

    # Check if result file already exists
    os.makedirs('results', exist_ok=True)
    ci_suffix = "_with_ci" if args.compute_ci else ""
    result_file = f'results/{model_name}_{dataset_name}_kfold{args.k}{ci_suffix}_metrics.txt'

    if os.path.exists(result_file) and not args.force_recompute:
        print(f"Result file {result_file} already exists. Skipping evaluation.")

        # Read the existing results file
        with open(result_file, 'r') as f:
            formatted_results = f.read()

        # Create a minimal metrics dictionary with the formatted results
        metrics = {
            'formatted_results': formatted_results,
            'accuracy': 0.0,
            'macro_f1': 0.0,
            'weighted_f1': 0.0,
            'auc': 0.0,
            'kfold_results': True  # Flag to indicate this is k-fold results
        }

        # Parse the formatted results to extract actual metrics
        try:
            # Extract mean accuracy
            acc_match = re.search(r'Overall Accuracy: (\d+\.\d+)% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
            if acc_match:
                metrics['accuracy'] = float(acc_match.group(1))
                metrics['accuracy_ci'] = (float(acc_match.group(2)), float(acc_match.group(3)))

            # Extract mean AUC if available
            auc_match = re.search(r'AUC: (\d+\.\d+)% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
            if auc_match:
                metrics['auc'] = float(auc_match.group(1))
                metrics['auc_ci'] = (float(auc_match.group(2)), float(auc_match.group(3)))

            # Extract mean F1 scores
            macro_f1_match = re.search(r'Macro F1 Score: (\d+\.\d+)% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
            if macro_f1_match:
                metrics['macro_f1'] = float(macro_f1_match.group(1))
                metrics['macro_f1_ci'] = (float(macro_f1_match.group(2)), float(macro_f1_match.group(3)))

            weighted_f1_match = re.search(r'Weighted F1 Score: (\d+\.\d+)% \(95% CI: (\d+\.\d+)% - (\d+\.\d+)%\)', formatted_results)
            if weighted_f1_match:
                metrics['weighted_f1'] = float(weighted_f1_match.group(1))
                metrics['weighted_f1_ci'] = (float(weighted_f1_match.group(2)), float(weighted_f1_match.group(3)))

        except Exception as e:
            print(f"Warning: Error parsing metrics from file: {e}")
            print("Using placeholder values for metrics.")

        return metrics

    # Set device
    device = torch.device(f"cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Build model and dataset
    model, preprocess = helper.build_model(model_name)

    # Build k-fold datasets
    print(f"Building k-fold dataset with k={args.k}, cache_enabled={args.cache_data}")
    folds = helper.build_kfold_dataset(
        dataset_name,
        preprocess,
        k=args.k,
        use_cache=args.cache_data,
        num_workers=args.num_workers,
        disable_progress_bar=args.disable_progress_bar
    )

    # Get the number of classes from the first fold
    num_classes = len(folds[0]['train'].label_dict)

    # Store metrics from all folds
    fold_metrics = []

    for fold_idx, fold_data in enumerate(folds):
        print(f"\n{'-'*60}")
        print(f"Training and evaluating fold {fold_idx + 1}/{args.k}")
        print(f"{'-'*60}")

        # Freeze all parameters in the base model
        for param in model.parameters():
            param.requires_grad = False

        # Move the base model to the correct device
        model = model.to(device)

        # Get the image encoder function
        def get_image_encoder(model):
            if model_name == 'ResNet50' or model_name == 'ViT-L':
                return model
            return lambda x: model.encode_image(x)

        model_function = get_image_encoder(model)

        # Detect feature dimension
        class ModelArgs:
            def __init__(self, model_name, gpu):
                self.model = model_name
                self.gpu = gpu

        model_args = ModelArgs(model_name, args.gpu)
        in_features = helper.detect_feature_dimension(model_function, model_args)

        # Create a wrapper function that ensures inputs are on the correct device
        def device_aware_encoder(x):
            with torch.no_grad():
                return model_function(x)

        # Build linear probe model for this fold
        fold_model = helper.build_linear_probe_model(device_aware_encoder, in_features, num_classes)
        fold_model = fold_model.to(device)

        # Create data loaders for this fold
        bs = args.batch_size
        train_loader = torch.utils.data.DataLoader(fold_data['train'], batch_size=bs, shuffle=True)
        test_loader = torch.utils.data.DataLoader(fold_data['test'], batch_size=bs, shuffle=False)

        print(f"Fold {fold_idx + 1}: Training set has {len(fold_data['train'])} samples")
        print(f"Fold {fold_idx + 1}: Test set has {len(fold_data['test'])} samples")

        # Define optimizer - only train the classifier parameters
        optimizer = torch.optim.Adam(fold_model.classifier.parameters(), lr=args.lr)

        # Get task type from dataset
        task_type = fold_data['train'].task_type

        # Define loss function
        criterion = torch.nn.CrossEntropyLoss()

        # Train the model for this fold (using train set as both train and val)
        fold_model, _, _ = helper.train_model(
            fold_model, train_loader, train_loader, criterion, optimizer, device,
            args.epochs, model_name, dataset_name, task_type=task_type
        )

        # Evaluate the model on the test set for this fold
        print(f"\nEvaluating fold {fold_idx + 1} on test set...")
        metrics = helper.evaluate(
            fold_model, test_loader, criterion, device,
            task_type=task_type, label_dict=fold_data['train'].label_dict,
            compute_ci=False, n_bootstraps=args.n_bootstraps  # Don't compute CI for individual folds
        )

        fold_metrics.append(metrics)
        print(f"Fold {fold_idx + 1} results:")
        print(f"  Accuracy: {metrics['accuracy']:.2f}%")
        if 'auc' in metrics:
            print(f"  AUC: {metrics['auc']:.2f}%")
        if 'macro_f1' in metrics:
            print(f"  Macro F1: {metrics['macro_f1']:.2f}%")
        if 'weighted_f1' in metrics:
            print(f"  Weighted F1: {metrics['weighted_f1']:.2f}%")

    # Compute mean and 95% CI across folds
    print(f"\n{'='*60}")
    print(f"Computing mean and 95% CI across {args.k} folds")
    print(f"{'='*60}")

    # Aggregate metrics across folds
    aggregated_metrics = {}

    # List of metrics to aggregate
    metrics_to_aggregate = ['accuracy', 'auc', 'macro_f1', 'weighted_f1']

    for metric in metrics_to_aggregate:
        if metric in fold_metrics[0]:
            values = [fold_metric[metric] for fold_metric in fold_metrics]
            mean_value = np.mean(values)
            std_value = np.std(values, ddof=1)  # Sample standard deviation

            # Compute 95% CI using t-distribution
            from scipy import stats
            ci_lower, ci_upper = stats.t.interval(0.95, len(values)-1, loc=mean_value, scale=std_value/np.sqrt(len(values)))

            aggregated_metrics[metric] = mean_value
            aggregated_metrics[f'{metric}_ci'] = (ci_lower, ci_upper)

    # Aggregate confusion matrix across folds
    if 'confusion_matrix' in fold_metrics[0]:
        # Get all confusion matrices
        confusion_matrices = [fold_metric['confusion_matrix'] for fold_metric in fold_metrics]

        # Compute mean confusion matrix
        mean_confusion_matrix = np.mean(confusion_matrices, axis=0)
        aggregated_metrics['confusion_matrix'] = mean_confusion_matrix

        # Also store individual fold confusion matrices for reference
        aggregated_metrics['fold_confusion_matrices'] = confusion_matrices

    # Create formatted results string
    formatted_results = f"K-Fold Cross-Validation Results ({args.k} folds)\n"
    formatted_results += f"{'='*50}\n"

    for metric in metrics_to_aggregate:
        if metric in aggregated_metrics:
            mean_val = aggregated_metrics[metric]
            ci_lower, ci_upper = aggregated_metrics[f'{metric}_ci']

            if metric == 'accuracy':
                formatted_results += f"Overall Accuracy: {mean_val:.2f}% (95% CI: {ci_lower:.2f}% - {ci_upper:.2f}%)\n"
            elif metric == 'auc':
                formatted_results += f"AUC: {mean_val:.2f}% (95% CI: {ci_lower:.2f}% - {ci_upper:.2f}%)\n"
            elif metric == 'macro_f1':
                formatted_results += f"Macro F1 Score: {mean_val:.2f}% (95% CI: {ci_lower:.2f}% - {ci_upper:.2f}%)\n"
            elif metric == 'weighted_f1':
                formatted_results += f"Weighted F1 Score: {mean_val:.2f}% (95% CI: {ci_lower:.2f}% - {ci_upper:.2f}%)\n"

    # Add confusion matrix if available
    if 'confusion_matrix' in aggregated_metrics:
        formatted_results += f"\nMean Confusion Matrix (averaged across {args.k} folds):\n"
        cm = aggregated_metrics['confusion_matrix']

        # Get class names from the first fold's label_dict
        if 'train' in folds[0] and hasattr(folds[0]['train'], 'label_dict'):
            label_dict = folds[0]['train'].label_dict
            class_names = [k for k, _ in sorted(label_dict.items(), key=lambda x: x[1])]
        else:
            class_names = [f"Class {i}" for i in range(cm.shape[0])]

        # Format confusion matrix
        header = "      " + " ".join(f"{i:8d}" for i in range(len(class_names)))
        formatted_results += header + "\n"
        formatted_results += "-" * len(header) + "\n"

        for i, row in enumerate(cm):
            formatted_results += f"{i:5d} " + " ".join(f"{cell:8.1f}" for cell in row) + "\n"

    # Add individual fold results
    formatted_results += f"\nIndividual Fold Results:\n"
    for i, fold_metric in enumerate(fold_metrics):
        formatted_results += f"Fold {i+1}: "
        fold_results = []
        for metric in metrics_to_aggregate:
            if metric in fold_metric:
                fold_results.append(f"{metric}={fold_metric[metric]:.2f}%")
        formatted_results += ", ".join(fold_results) + "\n"

    aggregated_metrics['formatted_results'] = formatted_results
    aggregated_metrics['kfold_results'] = True  # Flag to indicate this is k-fold results

    # Print formatted results
    print(formatted_results)

    # Save results to file
    with open(result_file, 'w') as f:
        f.write(formatted_results)
    print(f"\nResults saved to {result_file}")

    return aggregated_metrics


if __name__ == '__main__':
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Linear Probing for Cytology Downstream Tasks')

    # Add option to load configuration from YAML file
    parser.add_argument('--config', type=str, default='/home/<USER>/jh/cytofm/config/bash_config.yaml',
                        help='Path to YAML configuration file. If provided, parameters from this file will be used.')

    # Keep all original arguments for backward compatibility
    parser.add_argument('--models', nargs='+', default=['ResNet50', 'ViT-L', 'CLIP', 'SigLIP', 'SigLIP2-ViT-B', 'SigLIP2-ViT-L', 'SigLIP2-ViT-SO400M'],
                        help='List of model names to evaluate')
    parser.add_argument('--datasets', nargs='+', default=['Herlev', 'HiCervix', 'JinWooChoi', 'FNAC2019', 'LDCC', 'Sipakmed', 'Barcelona', 'BCI', 'BCFCI', 'BMCC'],
                        help='List of dataset names to evaluate')
    parser.add_argument('--gpu', type=int, default=7, help='GPU ID')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--compute_ci', action='store_true', default=True,
                        help='Compute bootstrap confidence intervals')
    parser.add_argument('--n_bootstraps', type=int, default=1000,
                        help='Number of bootstrap samples for confidence intervals')
    parser.add_argument('--metrics', nargs='+', default=['accuracy', 'auc', 'macro_f1'],
                        help='List of metrics to use for table generation (accuracy, auc, macro_f1, weighted_f1, etc.)')
    parser.add_argument('--force_recompute', action='store_true',
                        help='Force recomputation even if result files already exist')
    parser.add_argument('--cache_data', action='store_true',
                        help='Cache all images in memory for faster training and evaluation')
    parser.add_argument('--num_workers', type=int, default=None,
                        help='Number of worker threads for data loading and caching (default: half of CPU cores)')
    parser.add_argument('--disable_progress_bar', action='store_true',
                        help='Disable progress bar during data loading (useful for non-interactive terminals)')
    parser.add_argument('--kfold', action='store_true',
                        help='Use k-fold cross-validation instead of official train/val/test split')
    parser.add_argument('--k', type=int, default=3,
                        help='Number of folds for k-fold cross-validation (default: 3)')

    # Parse command line arguments
    cmd_args = parser.parse_args()

    # If config file is provided, load configuration from YAML
    if cmd_args.config:
        print(f"Loading configuration from: {cmd_args.config}")
        args = load_config_from_yaml(cmd_args.config)
        print("Configuration loaded successfully from YAML file.")
    else:
        # Use command line arguments
        args = cmd_args

    # Create timestamp for this run
    timestamp = time.strftime("%Y%m%d-%H%M%S")

    # Store results for all model-dataset combinations
    all_results = []
    skipped_evaluations = []

    # Evaluate each model on each dataset
    for model_name in args.models:
        for dataset_name in args.datasets:
            try:
                if args.kfold:
                    # K-fold cross-validation evaluation
                    ci_suffix = "_with_ci" if args.compute_ci else ""
                    result_file = f'results/{model_name}_{dataset_name}_kfold{args.k}{ci_suffix}_metrics.txt'

                    # Check if this will be a skipped evaluation for summary reporting
                    if os.path.exists(result_file) and not args.force_recompute:
                        skipped_evaluations.append((model_name, dataset_name))

                    # Perform the k-fold evaluation (or load from file)
                    metrics = evaluate_model_on_dataset_kfold(model_name, dataset_name, args)
                else:
                    # Standard train/val/test evaluation
                    ci_suffix = "_with_ci" if args.compute_ci else ""
                    result_file = f'results/{model_name}_{dataset_name}{ci_suffix}_metrics.txt'

                    # Check if this will be a skipped evaluation for summary reporting
                    if os.path.exists(result_file) and not args.force_recompute:
                        skipped_evaluations.append((model_name, dataset_name))

                    # Perform the evaluation (or load from file)
                    metrics = evaluate_model_on_dataset(model_name, dataset_name, args)

                # Store results
                all_results.append((model_name, dataset_name, metrics))

            except Exception as e:
                print(f"Error evaluating {model_name} on {dataset_name}: {e}")
                continue

    # Print summary of skipped evaluations
    if skipped_evaluations:
        print(f"\n{'-'*80}")
        print(f"Skipped {len(skipped_evaluations)} evaluations (results already exist):")
        for model_name, dataset_name in skipped_evaluations:
            print(f"  - {model_name} on {dataset_name}")
        print(f"Use --force_recompute to force re-evaluation of all model-dataset pairs")
        print(f"{'-'*80}")

    # Collect and organize results
    results_dict = utils.collect_results(all_results)

    # Generate tables for each metric
    ci_suffix = "_with_ci" if args.compute_ci else ""
    kfold_suffix = f"_kfold{args.k}" if args.kfold else ""
    csv_paths = []
    latex_paths = []

    for metric in args.metrics:
        # Generate CSV table
        csv_path = f'results/table_{metric}{kfold_suffix}{ci_suffix}_{timestamp}.csv'
        utils.generate_csv_table(results_dict, csv_path, metric=metric)
        csv_paths.append(csv_path)

        # Generate LaTeX table
        latex_path = f'results/table_{metric}{kfold_suffix}{ci_suffix}_{timestamp}.tex'
        if args.kfold:
            caption = f"K-fold cross-validation results for {metric} metric ({args.k} folds)"
        else:
            caption = f"Evaluation results for {metric} metric"
        label = f"tab:{metric}_results"
        utils.generate_latex_table(
            results_dict, latex_path, metric=metric,
            caption=caption, label=label
        )
        latex_paths.append(latex_path)

    # Generate a combined table with all metrics
    combined_csv_path = f'results/table_combined{kfold_suffix}{ci_suffix}_{timestamp}.csv'
    combined_latex_path = f'results/table_combined{kfold_suffix}{ci_suffix}_{timestamp}.tex'

    if args.kfold:
        combined_caption = f"Combined k-fold cross-validation results for multiple metrics ({args.k} folds)"
    else:
        combined_caption = "Combined evaluation results for multiple metrics"

    # For k-fold results, we always have confidence intervals
    combined_compute_ci = args.compute_ci or args.kfold

    utils.generate_combined_table(
        results_dict, combined_csv_path, combined_latex_path,
        metrics=args.metrics, compute_ci=combined_compute_ci,
        caption=combined_caption,
        label="tab:combined_results"
    )

    print(f"\nResults tables generated:")
    print("Individual metric tables:")
    for i, metric in enumerate(args.metrics):
        print(f"  {metric}: CSV - {csv_paths[i]}, LaTeX - {latex_paths[i]}")

    print("\nCombined tables with all metrics:")
    print(f"  CSV: {combined_csv_path}")
    print(f"  LaTeX: {combined_latex_path}")

    if args.kfold:
        print(f"\nNote: Results are from {args.k}-fold cross-validation with mean ± 95% CI across folds.")
    elif not args.compute_ci:
        print("\nNote: Confidence intervals were not computed. Run with --compute_ci to include them.")

