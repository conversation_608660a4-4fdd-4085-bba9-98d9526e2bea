#!/bin/bash

# Training script for linear probing experiments
# Usage: ./scripts/train.sh [config_file]

# Default configuration file
CONFIG_FILE="config/bash_config.yaml"

# Use provided config file if given
if [[ $# -gt 0 ]]; then
    CONFIG_FILE="$1"
fi

# Show help if requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "Usage: $0 [config_file]"
    echo ""
    echo "Arguments:"
    echo "  config_file    Path to YAML configuration file (default: config/bash_config.yaml)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use default config"
    echo "  $0 config/custom_config.yaml         # Use custom config"
    echo ""
    exit 0
fi

# Check if config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

echo "Starting training with config: $CONFIG_FILE"

# Run the training script
python main.py --config "$CONFIG_FILE"
